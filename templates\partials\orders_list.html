<div class="bg-white shadow-sm sm:shadow overflow-hidden sm:rounded-md">
    {% if orders %}
        <ul class="divide-y divide-gray-200">
            {% for order in orders %}
            <li>
                <div class="px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <div class="flex items-center space-x-2 sm:space-x-3">
                            <p class="text-xs sm:text-sm font-medium text-blue-600 truncate">
                                Order #{{ order.id }}
                            </p>
                            <div class="flex-shrink-0">
                                <p class="px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full
                                   {% if order.status == 'delivered' %}bg-green-100 text-green-800
                                   {% elif order.status == 'out_for_delivery' %}bg-yellow-100 text-yellow-800
                                   {% elif order.status == 'confirmed' %}bg-blue-100 text-blue-800
                                   {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                   {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ order.get_status_display }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center text-xs sm:text-sm text-gray-500">
                            <p class="hidden sm:block">{{ order.created_at|date:"M d, Y g:i A" }}</p>
                            <p class="sm:hidden">{{ order.created_at|date:"M d, Y" }}</p>
                        </div>
                    </div>
                    <div class="mt-2 space-y-2 sm:space-y-0 sm:flex sm:justify-between">
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-6 text-xs sm:text-sm">
                            <div class="flex items-center text-gray-500">
                                <svg class="flex-shrink-0 mr-1 sm:mr-1.5 h-3 w-3 sm:h-4 sm:w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="truncate">{{ order.product.name }} x{{ order.quantity }}</span>
                            </div>
                            <div class="flex items-center text-gray-500">
                                <svg class="flex-shrink-0 mr-1 sm:mr-1.5 h-3 w-3 sm:h-4 sm:w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                </svg>
                                ₱{{ order.total_amount }}
                            </div>
                            <div class="flex items-center text-gray-500">
                                <svg class="flex-shrink-0 mr-1 sm:mr-1.5 h-3 w-3 sm:h-4 sm:w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                {{ order.get_delivery_type_display }}
                            </div>
                        </div>
                        <div class="flex items-center justify-end">
                            <a href="{% url 'core:order_detail' order.id %}"
                               class="font-medium text-blue-600 hover:text-blue-500 text-xs sm:text-sm transition-colors duration-200 tap-target"
                               hx-get="{% url 'core:order_detail' order.id %}"
                               hx-target="#order-detail-modal"
                               hx-swap="innerHTML"
                               @click="$dispatch('open-modal', 'order-detail')">
                                View Details
                            </a>
                        </div>
                    </div>
                    
                    {% if order.notes %}
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">
                            <span class="font-medium">Notes:</span> {{ order.notes }}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </li>
            {% endfor %}
        </ul>
    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
            <p class="mt-1 text-sm text-gray-500">
                {% if status_filter %}
                    No orders with status "{{ status_filter|title }}".
                {% else %}
                    You haven't placed any orders yet.
                {% endif %}
            </p>
            <div class="mt-6">
                <a href="{% url 'core:place_order' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                    </svg>
                    Place Your First Order
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Order Detail Modal -->
<div id="order-detail-modal" 
     x-data="{ open: false }" 
     @open-modal.window="if ($event.detail === 'order-detail') open = true"
     @close-modal.window="if ($event.detail === 'order-detail') open = false"
     x-show="open" 
     x-cloak
     class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" @click="open = false">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <!-- Modal content will be loaded here by HTMX -->
        </div>
    </div>
</div>
