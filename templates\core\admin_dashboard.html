{% extends 'base.html' %}

{% block title %}Admin Dashboard - Prycegas{% endblock %}

{% block content %}
<div class="px-0 sm:px-0">
    <!-- Header -->
    <div class="mb-6 sm:mb-8 px-4 sm:px-0">
        <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Dealer Dashboard</h1>
        <p class="mt-2 text-sm sm:text-base text-gray-600">Manage orders, inventory, and deliveries for Prycegas Tambulig Station.</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 gap-3 sm:gap-6 md:grid-cols-4 mb-6 sm:mb-8 px-4 sm:px-0">
        <div class="bg-white overflow-hidden shadow-sm sm:shadow rounded-lg hover:shadow-md transition-shadow duration-200">
            <div class="p-3 sm:p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                        </svg>
                    </div>
                    <div class="ml-3 sm:ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-xs sm:text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                            <dd class="text-base sm:text-lg lg:text-xl font-medium text-gray-900">{{ total_orders }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Orders</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ pending_orders }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Delivered Orders</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ delivered_orders }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Low Stock Items</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ low_stock_items.count }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="grid grid-cols-1 gap-4 sm:gap-6 lg:grid-cols-3 mb-6 sm:mb-8 px-4 sm:px-0">
        <div class="bg-white shadow-sm sm:shadow rounded-lg p-4 sm:p-6">
            <h3 class="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Quick Actions</h3>
            <div class="space-y-2 sm:space-y-3">
                <a href="{% url 'core:orders' %}" class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 sm:py-3 px-3 sm:px-4 rounded text-center text-sm sm:text-base transition-colors duration-200">
                    Manage Orders
                </a>
                <a href="{% url 'core:inventory' %}" class="block w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 sm:py-3 px-3 sm:px-4 rounded text-center text-sm sm:text-base transition-colors duration-200">
                    View Inventory
                </a>
                <a href="{% url 'core:delivery_logs' %}" class="block w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 sm:py-3 px-3 sm:px-4 rounded text-center text-sm sm:text-base transition-colors duration-200">
                    Log Delivery
                </a>
                <a href="{% url 'core:reports' %}" class="block w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 sm:py-3 px-3 sm:px-4 rounded text-center text-sm sm:text-base transition-colors duration-200">
                    View Reports
                </a>
            </div>
        </div>
        
        <!-- Low Stock Alert -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Low Stock Alert</h3>
            {% if low_stock_items %}
                <div class="space-y-2">
                    {% for item in low_stock_items %}
                    <div class="flex justify-between items-center p-2 bg-red-50 rounded">
                        <span class="text-sm font-medium text-red-800">{{ item.product.name }}</span>
                        <span class="text-sm text-red-600">{{ item.current_stock }} left</span>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-sm text-gray-500">All items are well stocked.</p>
            {% endif %}
        </div>
        
        <!-- Recent Deliveries -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Deliveries</h3>
            {% if recent_deliveries %}
                <div class="space-y-2">
                    {% for delivery in recent_deliveries %}
                    <div class="text-sm">
                        <div class="font-medium text-gray-900">{{ delivery.product.name }}</div>
                        <div class="text-gray-500">{{ delivery.quantity_received }} units - {{ delivery.delivery_date|date:"M d" }}</div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <p class="text-sm text-gray-500">No recent deliveries.</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Recent Orders -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Orders</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Latest customer orders requiring attention.</p>
            </div>
            <a href="{% url 'core:orders' %}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                View All →
            </a>
        </div>
        <ul class="divide-y divide-gray-200">
            {% for order in recent_orders %}
            <li>
                <div class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-blue-600 truncate">
                                Order #{{ order.id }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                   {% if order.status == 'delivered' %}bg-green-100 text-green-800
                                   {% elif order.status == 'out_for_delivery' %}bg-yellow-100 text-yellow-800
                                   {% elif order.status == 'confirmed' %}bg-blue-100 text-blue-800
                                   {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                   {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ order.get_status_display }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <p>{{ order.created_at|date:"M d, Y" }}</p>
                        </div>
                    </div>
                    <div class="mt-2 sm:flex sm:justify-between">
                        <div class="sm:flex">
                            <p class="flex items-center text-sm text-gray-500">
                                {{ order.customer.user.get_full_name|default:order.customer.user.username }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                {{ order.product.name }} x{{ order.quantity }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                ₱{{ order.total_amount }}
                            </p>
                        </div>
                    </div>
                </div>
            </li>
            {% empty %}
            <li class="px-4 py-4 sm:px-6">
                <p class="text-sm text-gray-500">No recent orders.</p>
            </li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endblock %}
