from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    path('', views.home, name='home'),
    path('dashboard/', views.dashboard, name='dashboard'),

    # Customer URLs
    path('customer/', views.customer_dashboard, name='customer_dashboard'),
    path('order/', views.place_order, name='place_order'),
    path('orders/', views.my_orders, name='my_orders'),
    path('orders/<int:order_id>/', views.order_detail, name='order_detail'),

    # Admin URLs
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('manage/orders/', views.orders_management, name='orders'),
    path('manage/orders/<int:order_id>/update/', views.update_order_status, name='update_order_status'),
    path('manage/inventory/', views.inventory_management, name='inventory'),
    path('manage/deliveries/', views.delivery_logs, name='delivery_logs'),
    path('reports/', views.reports, name='reports'),
]
