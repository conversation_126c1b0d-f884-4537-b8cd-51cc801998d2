{% extends 'base.html' %}

{% block title %}Delivery Logs - Prycegas{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Delivery Logs</h1>
            <p class="mt-2 text-sm text-gray-700">Log new deliveries from distributors and view delivery history.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <a href="{% url 'core:admin_dashboard' %}" class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Add New Delivery Form -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Log New Delivery</h3>
            
            <form method="post" hx-post="{% url 'core:delivery_logs' %}" hx-target="#delivery-form" hx-swap="outerHTML" x-data="deliveryForm()">
                <div id="delivery-form">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <!-- Product -->
                        <div>
                            <label for="{{ form.product.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Product
                            </label>
                            {{ form.product }}
                            {% if form.product.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.product.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Quantity Received -->
                        <div>
                            <label for="{{ form.quantity_received.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Quantity Received
                            </label>
                            {{ form.quantity_received }}
                            {% if form.quantity_received.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.quantity_received.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Supplier Name -->
                        <div>
                            <label for="{{ form.supplier_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Supplier Name
                            </label>
                            {{ form.supplier_name }}
                            {% if form.supplier_name.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.supplier_name.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Delivery Date -->
                        <div>
                            <label for="{{ form.delivery_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Delivery Date
                            </label>
                            {{ form.delivery_date }}
                            {% if form.delivery_date.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.delivery_date.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Cost per Unit -->
                        <div>
                            <label for="{{ form.cost_per_unit.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Cost per Unit (₱)
                            </label>
                            {{ form.cost_per_unit }}
                            {% if form.cost_per_unit.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.cost_per_unit.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Invoice Number -->
                        <div>
                            <label for="{{ form.invoice_number.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Invoice Number (Optional)
                            </label>
                            {{ form.invoice_number }}
                            {% if form.invoice_number.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.invoice_number.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Notes -->
                    <div class="mt-6">
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Notes (Optional)
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.notes.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Total Cost Display -->
                    <div class="mt-6 bg-gray-50 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-medium text-gray-900">Total Cost:</span>
                            <span class="text-xl font-bold text-blue-600" x-text="'₱' + totalCost"></span>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" @click="resetForm()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Reset
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Log Delivery
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Delivery History -->
    <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Delivery History</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Recent deliveries from distributors.</p>
        </div>
        <ul class="divide-y divide-gray-200">
            {% for log in logs %}
            <li>
                <div class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-blue-600 truncate">
                                {{ log.product.name }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    +{{ log.quantity_received }} units
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <p>{{ log.delivery_date|date:"M d, Y g:i A" }}</p>
                        </div>
                    </div>
                    <div class="mt-2 sm:flex sm:justify-between">
                        <div class="sm:flex sm:flex-wrap sm:space-x-6">
                            <p class="flex items-center text-sm text-gray-500">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2v8h12V6H4z" clip-rule="evenodd"></path>
                                </svg>
                                {{ log.supplier_name }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                </svg>
                                ₱{{ log.cost_per_unit }} per unit
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                Total: ₱{{ log.total_cost }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                Logged by {{ log.created_by.get_full_name|default:log.created_by.username }}
                            </p>
                        </div>
                    </div>
                    
                    {% if log.invoice_number %}
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">
                            <span class="font-medium">Invoice:</span> {{ log.invoice_number }}
                        </p>
                    </div>
                    {% endif %}
                    
                    {% if log.notes %}
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">
                            <span class="font-medium">Notes:</span> {{ log.notes }}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </li>
            {% empty %}
            <li class="px-4 py-4 sm:px-6">
                <p class="text-sm text-gray-500">No delivery logs yet.</p>
            </li>
            {% endfor %}
        </ul>
    </div>
</div>

<script>
function deliveryForm() {
    return {
        quantity: 0,
        costPerUnit: 0,
        
        get totalCost() {
            return (this.quantity * this.costPerUnit).toFixed(2);
        },
        
        resetForm() {
            document.querySelector('form').reset();
            this.quantity = 0;
            this.costPerUnit = 0;
        },
        
        init() {
            // Watch for changes in quantity and cost fields
            this.$watch('quantity', () => this.updateTotal());
            this.$watch('costPerUnit', () => this.updateTotal());
        },
        
        updateTotal() {
            // This method can be used for additional calculations if needed
        }
    }
}
</script>
{% endblock %}
