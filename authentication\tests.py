from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse


class AuthenticationTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_logout_get_request(self):
        """Test that logout works with GET request"""
        # Login first
        self.client.login(username='testuser', password='testpass123')

        # Test GET logout
        response = self.client.get(reverse('auth:logout'))
        self.assertEqual(response.status_code, 302)  # Redirect after logout

        # Verify user is logged out
        response = self.client.get(reverse('core:customer_dashboard'))
        self.assertEqual(response.status_code, 302)  # Should redirect to login

    def test_logout_post_request(self):
        """Test that logout works with POST request"""
        # Login first
        self.client.login(username='testuser', password='testpass123')

        # Test POST logout
        response = self.client.post(reverse('auth:logout'))
        self.assertEqual(response.status_code, 302)  # Redirect after logout

        # Verify user is logged out
        response = self.client.get(reverse('core:customer_dashboard'))
        self.assertEqual(response.status_code, 302)  # Should redirect to login
