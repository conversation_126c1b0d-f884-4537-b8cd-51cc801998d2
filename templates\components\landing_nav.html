<!-- Landing Navigation Component -->
<nav class="fixed w-full z-50 transition-all duration-300" 
     x-data="{ scrolled: false }"
     x-init="window.addEventListener('scroll', () => { scrolled = window.scrollY > 50 })"
     :class="scrolled ? 'bg-opacity-95 backdrop-blur-sm shadow-lg' : 'bg-opacity-80'"
     style="background-color: var(--background-dark);">
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16 sm:h-20">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{% url 'core:home' %}" class="flex items-center space-x-2">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center"
                         style="background: var(--energy-gradient);">
                        <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h1 class="text-xl sm:text-2xl font-bold text-energy">Prycegas</h1>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="{% url 'core:home' %}" class="text-high-contrast font-semibold transition-all duration-300 hover:text-energy hover:scale-105 px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10">Home</a>
                <a href="#features" class="text-high-contrast font-semibold transition-all duration-300 hover:text-energy hover:scale-105 px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10">Features</a>
                <a href="#about" class="text-high-contrast font-semibold transition-all duration-300 hover:text-energy hover:scale-105 px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10">About</a>
                <a href="#contact" class="text-high-contrast font-semibold transition-all duration-300 hover:text-energy hover:scale-105 px-3 py-2 rounded-lg hover:bg-white hover:bg-opacity-10">Contact</a>
            </div>
            
            <!-- Auth Buttons -->
            <div class="flex items-center space-x-4">
                {% if user.is_authenticated %}
                    <a href="{% if user.is_staff %}{% url 'core:admin_dashboard' %}{% else %}{% url 'core:customer_dashboard' %}{% endif %}"
                       class="cta-button px-6 py-3 text-sm font-bold">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Dashboard
                    </a>
                {% else %}
                    <a href="{% url 'auth:login' %}"
                       class="btn-secondary-landing px-6 py-3 text-sm font-bold">
                        <span>Login</span>
                    </a>
                    <a href="{% url 'auth:register' %}"
                       class="cta-button px-6 py-3 text-sm font-bold">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Get Started
                    </a>
                {% endif %}
                
                <!-- Mobile menu button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen"
                        class="md:hidden p-3 rounded-lg transition-all duration-300 hover:scale-110"
                        style="color: var(--text-light); background: rgba(255, 255, 255, 0.1);">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Mobile menu -->
    <div x-show="mobileMenuOpen"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform -translate-y-4"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-4"
         class="md:hidden backdrop-blur-lg"
         style="background: rgba(26, 26, 26, 0.95); border-top: 2px solid var(--primary-orange);">
        <div class="px-6 pt-4 pb-6 space-y-3">
            <a href="{% url 'core:home' %}" class="block px-4 py-3 rounded-lg text-lg font-semibold text-high-contrast transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-energy">Home</a>
            <a href="#features" class="block px-4 py-3 rounded-lg text-lg font-semibold text-high-contrast transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-energy">Features</a>
            <a href="#about" class="block px-4 py-3 rounded-lg text-lg font-semibold text-high-contrast transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-energy">About</a>
            <a href="#contact" class="block px-4 py-3 rounded-lg text-lg font-semibold text-high-contrast transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-energy">Contact</a>

            <!-- Mobile Auth Buttons -->
            <div class="pt-4 space-y-3 border-t border-gray-600">
                {% if not user.is_authenticated %}
                    <a href="{% url 'auth:login' %}" class="block btn-secondary-landing px-4 py-3 text-center text-lg font-bold">
                        <span>Login</span>
                    </a>
                    <a href="{% url 'auth:register' %}" class="block cta-button px-4 py-3 text-center text-lg font-bold">
                        Get Started
                    </a>
                {% else %}
                    <a href="{% if user.is_staff %}{% url 'core:admin_dashboard' %}{% else %}{% url 'core:customer_dashboard' %}{% endif %}"
                       class="block cta-button px-4 py-3 text-center text-lg font-bold">
                        Dashboard
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</nav>
