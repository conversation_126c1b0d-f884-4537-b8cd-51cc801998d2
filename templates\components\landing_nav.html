<!-- Landing Navigation Component -->
<nav class="fixed w-full z-50 transition-all duration-300" 
     x-data="{ scrolled: false }"
     x-init="window.addEventListener('scroll', () => { scrolled = window.scrollY > 50 })"
     :class="scrolled ? 'bg-opacity-95 backdrop-blur-sm shadow-lg' : 'bg-opacity-80'"
     style="background-color: var(--background-dark);">
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16 sm:h-20">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{% url 'core:home' %}" class="flex items-center space-x-2">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-lg flex items-center justify-center"
                         style="background: var(--energy-gradient);">
                        <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h1 class="text-xl sm:text-2xl font-bold text-energy">Prycegas</h1>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="{% url 'core:home' %}" class="professional-text transition-colors duration-200" 
                   style="color: var(--text-light);"
                   onmouseover="this.style.color='var(--primary-orange)'"
                   onmouseout="this.style.color='var(--text-light)'">Home</a>
                <a href="#features" class="professional-text transition-colors duration-200" 
                   style="color: var(--text-light);"
                   onmouseover="this.style.color='var(--primary-orange)'"
                   onmouseout="this.style.color='var(--text-light)'">Features</a>
                <a href="#about" class="professional-text transition-colors duration-200" 
                   style="color: var(--text-light);"
                   onmouseover="this.style.color='var(--primary-orange)'"
                   onmouseout="this.style.color='var(--text-light)'">About</a>
                <a href="#contact" class="professional-text transition-colors duration-200" 
                   style="color: var(--text-light);"
                   onmouseover="this.style.color='var(--primary-orange)'"
                   onmouseout="this.style.color='var(--text-light)'">Contact</a>
            </div>
            
            <!-- Auth Buttons -->
            <div class="flex items-center space-x-4">
                {% if user.is_authenticated %}
                    <a href="{% if user.is_staff %}{% url 'core:admin_dashboard' %}{% else %}{% url 'core:customer_dashboard' %}{% endif %}" 
                       class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105"
                       style="background: var(--energy-gradient); color: white; box-shadow: var(--professional-shadow);">
                        Dashboard
                    </a>
                {% else %}
                    <a href="{% url 'auth:login' %}" 
                       class="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                       style="color: var(--text-light); border: 1px solid var(--border-dark);"
                       onmouseover="this.style.backgroundColor='var(--background-dark-secondary)'"
                       onmouseout="this.style.backgroundColor='transparent'">
                        Login
                    </a>
                    <a href="{% url 'auth:register' %}" 
                       class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105"
                       style="background: var(--energy-gradient); color: white; box-shadow: var(--professional-shadow);">
                        Get Started
                    </a>
                {% endif %}
                
                <!-- Mobile menu button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen" 
                        class="md:hidden p-2 rounded-lg transition-colors duration-200"
                        style="color: var(--text-light);"
                        onmouseover="this.style.backgroundColor='var(--background-dark-secondary)'"
                        onmouseout="this.style.backgroundColor='transparent'">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Mobile menu -->
    <div x-show="mobileMenuOpen" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="md:hidden"
         style="background-color: var(--background-dark-secondary); border-top: 1px solid var(--border-dark);">
        <div class="px-4 pt-2 pb-3 space-y-1">
            <a href="{% url 'core:home' %}" class="block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
               style="color: var(--text-light);"
               onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
               onmouseout="this.style.backgroundColor='transparent'">Home</a>
            <a href="#features" class="block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
               style="color: var(--text-light);"
               onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
               onmouseout="this.style.backgroundColor='transparent'">Features</a>
            <a href="#about" class="block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
               style="color: var(--text-light);"
               onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
               onmouseout="this.style.backgroundColor='transparent'">About</a>
            <a href="#contact" class="block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
               style="color: var(--text-light);"
               onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
               onmouseout="this.style.backgroundColor='transparent'">Contact</a>
        </div>
    </div>
</nav>
