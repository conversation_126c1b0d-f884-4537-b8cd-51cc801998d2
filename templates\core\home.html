{% extends 'base/landing.html' %}

{% block title %}Welcome - Prycegas{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center pt-20 pb-16">
    <!-- Background Pattern -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 rounded-full opacity-10" style="background: var(--energy-gradient);"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 rounded-full opacity-5" style="background: var(--energy-gradient);"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-slide-up">
            <h1 class="text-4xl sm:text-5xl lg:text-7xl font-bold mb-8 leading-tight">
                <span class="text-energy">Prycegas</span><br>
                <span class="text-high-contrast text-4xl sm:text-5xl lg:text-6xl">LPG Distribution</span>
            </h1>
            <p class="text-xl sm:text-2xl lg:text-3xl mb-12 max-w-4xl mx-auto text-medium-contrast leading-relaxed">
                Your trusted partner for <span class="text-high-contrast font-semibold">reliable</span>,
                <span class="text-high-contrast font-semibold">safe</span>, and
                <span class="text-high-contrast font-semibold">efficient</span> LPG delivery services.
            </p>
            <p class="text-lg sm:text-xl mb-12 max-w-3xl mx-auto text-low-contrast">
                Powering homes and businesses with professional gas solutions across the region.
            </p>

            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
                {% if user.is_authenticated %}
                    <a href="{% if user.is_staff %}{% url 'core:admin_dashboard' %}{% else %}{% url 'core:customer_dashboard' %}{% endif %}"
                       class="cta-button px-10 py-5 text-xl font-bold">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Go to Dashboard
                    </a>
                {% else %}
                    <a href="{% url 'auth:register' %}"
                       class="cta-button px-10 py-5 text-xl font-bold">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Get Started Today
                    </a>
                    <a href="{% url 'auth:login' %}"
                       class="btn-secondary-landing px-10 py-5 text-xl font-bold">
                        <span>Sign In</span>
                    </a>
                {% endif %}
            </div>

            <!-- Scroll Indicator -->
            <div class="scroll-indicator">
                <svg class="w-6 h-6 mx-auto" style="color: var(--primary-orange);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-24 animate-on-scroll" style="background: linear-gradient(180deg, var(--background-dark) 0%, var(--background-dark-secondary) 100%);">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-20">
            <h2 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-8">
                <span class="text-high-contrast">Why Choose </span>
                <span class="text-energy">Prycegas</span>
                <span class="text-high-contrast">?</span>
            </h2>
            <p class="text-xl sm:text-2xl text-medium-contrast max-w-4xl mx-auto leading-relaxed">
                We deliver <span class="text-high-contrast font-semibold">excellence</span> in every aspect of LPG distribution,
                ensuring <span class="text-energy font-semibold">safety</span>,
                <span class="text-energy font-semibold">reliability</span>, and
                <span class="text-energy font-semibold">customer satisfaction</span>.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            <!-- Feature 1 -->
            <div class="feature-card p-10 text-center animate-float group">
                <div class="w-20 h-20 mx-auto mb-8 rounded-full flex items-center justify-center relative"
                     style="background: var(--energy-gradient); box-shadow: var(--glow-orange);">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <div class="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </div>
                <h3 class="text-2xl font-bold mb-6 text-high-contrast group-hover:text-energy transition-colors duration-300">Safety First</h3>
                <p class="text-lg text-medium-contrast leading-relaxed">
                    Rigorous safety protocols and <span class="text-high-contrast font-semibold">certified equipment</span>
                    ensure secure handling and delivery of LPG products with zero compromise.
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="feature-card p-10 text-center animate-float group" style="animation-delay: 0.2s;">
                <div class="w-20 h-20 mx-auto mb-8 rounded-full flex items-center justify-center relative"
                     style="background: var(--energy-gradient); box-shadow: var(--glow-orange);">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <div class="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </div>
                <h3 class="text-2xl font-bold mb-6 text-high-contrast group-hover:text-energy transition-colors duration-300">Lightning Fast</h3>
                <p class="text-lg text-medium-contrast leading-relaxed">
                    <span class="text-high-contrast font-semibold">Quick and efficient</span> delivery services
                    to keep your operations running without interruption or delay.
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="feature-card p-10 text-center animate-float group" style="animation-delay: 0.4s;">
                <div class="w-20 h-20 mx-auto mb-8 rounded-full flex items-center justify-center relative"
                     style="background: var(--energy-gradient); box-shadow: var(--glow-orange);">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div class="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </div>
                <h3 class="text-2xl font-bold mb-6 text-high-contrast group-hover:text-energy transition-colors duration-300">24/7 Support</h3>
                <p class="text-lg text-medium-contrast leading-relaxed">
                    <span class="text-high-contrast font-semibold">Round-the-clock</span> customer support
                    to address your needs and ensure continuous, reliable service.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-24 relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0" style="background: linear-gradient(135deg, var(--background-dark-secondary) 0%, var(--background-dark) 50%, var(--background-dark-tertiary) 100%);">
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full opacity-10" style="background: var(--energy-gradient);"></div>
    </div>

    <div class="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-8">
            <span class="text-high-contrast">Ready to </span>
            <span class="text-energy">Power Up</span>
            <span class="text-high-contrast">?</span>
        </h2>
        <p class="text-xl sm:text-2xl text-medium-contrast mb-12 max-w-4xl mx-auto leading-relaxed">
            Join hundreds of satisfied customers who trust Prycegas for their LPG distribution needs.
            <span class="text-high-contrast font-semibold">Start your journey today</span> and experience the difference.
        </p>

        <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
            {% if not user.is_authenticated %}
                <a href="{% url 'auth:register' %}"
                   class="cta-button px-12 py-6 text-2xl font-bold">
                    <svg class="w-7 h-7 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Start Your Journey
                </a>
                <a href="{% url 'auth:login' %}"
                   class="btn-secondary-landing px-12 py-6 text-2xl font-bold">
                    <span>I'm Already a Customer</span>
                </a>
            {% else %}
                <a href="{% if user.is_staff %}{% url 'core:admin_dashboard' %}{% else %}{% url 'core:customer_dashboard' %}{% endif %}"
                   class="cta-button px-12 py-6 text-2xl font-bold">
                    <svg class="w-7 h-7 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Access Dashboard
                </a>
            {% endif %}
        </div>

        <!-- Trust Indicators -->
        <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 opacity-60">
            <div class="text-center">
                <div class="text-3xl font-bold text-energy mb-2">500+</div>
                <div class="text-sm text-medium-contrast">Happy Customers</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-energy mb-2">24/7</div>
                <div class="text-sm text-medium-contrast">Support Available</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-energy mb-2">100%</div>
                <div class="text-sm text-medium-contrast">Safety Certified</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-energy mb-2">5★</div>
                <div class="text-sm text-medium-contrast">Customer Rating</div>
            </div>
        </div>
    </div>
</section>

        <div class="mx-auto max-w-4xl py-16 sm:py-24 md:py-32 lg:py-48 xl:py-56">
            <div class="text-center">
                <h1 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight text-gray-900 leading-tight">
                    Vios Prycegas Tambulig Station
                </h1>
                <p class="mt-4 sm:mt-6 text-base sm:text-lg lg:text-xl leading-6 sm:leading-8 text-gray-600 max-w-3xl mx-auto px-4">
                    Your trusted LPG dealer in Tambulig, Zamboanga del Sur. Order your LPG tanks online with easy delivery and pickup options.
                </p>
                <div class="mt-6 sm:mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-x-6">
                    {% if user.is_authenticated %}
                        <a href="{% url 'core:dashboard' %}" class="w-full sm:w-auto rounded-md bg-blue-600 px-6 sm:px-3.5 py-3 sm:py-2.5 text-sm sm:text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors duration-200">
                            Go to Dashboard
                        </a>
                    {% else %}
                        <a href="{% url 'auth:register' %}" class="w-full sm:w-auto rounded-md bg-blue-600 px-6 sm:px-3.5 py-3 sm:py-2.5 text-sm sm:text-base font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors duration-200">
                            Get Started
                        </a>
                        <a href="{% url 'auth:login' %}" class="text-sm sm:text-base font-semibold leading-6 text-gray-900 hover:text-blue-600 transition-colors duration-200">
                            Sign In <span aria-hidden="true">→</span>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]">
            <div class="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"></div>
        </div>
    </div>
    
    <!-- Features section -->
    <div class="py-16 sm:py-24 lg:py-32">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div class="mx-auto max-w-3xl text-center lg:max-w-4xl">
                <h2 class="text-sm sm:text-base font-semibold leading-7 text-blue-600">Efficient Service</h2>
                <p class="mt-2 text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
                    Everything you need for LPG ordering
                </p>
                <p class="mt-4 sm:mt-6 text-base sm:text-lg leading-6 sm:leading-8 text-gray-600 max-w-2xl mx-auto">
                    Our digital platform makes it easy to order LPG tanks, track deliveries, and manage your account from anywhere.
                </p>
            </div>
            <div class="mx-auto mt-12 sm:mt-16 lg:mt-24 max-w-7xl">
                <dl class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:gap-8 xl:gap-x-12 xl:gap-y-16">
                    <div class="relative pl-12 sm:pl-16">
                        <dt class="text-base sm:text-lg font-semibold leading-6 sm:leading-7 text-gray-900">
                            <div class="absolute left-0 top-0 flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-lg bg-blue-600">
                                <svg class="h-4 w-4 sm:h-6 sm:w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                                </svg>
                            </div>
                            Online Ordering
                        </dt>
                        <dd class="mt-2 text-sm sm:text-base leading-6 sm:leading-7 text-gray-600">
                            Place your LPG orders online 24/7. Choose from different tank sizes and delivery options.
                        </dd>
                    </div>
                    <div class="relative pl-16">
                        <dt class="text-base font-semibold leading-7 text-gray-900">
                            <div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 01-3 0V6a3 3 0 013-3h6a3 3 0 013 3v7.5a1.5 1.5 0 01-3 0V6a1.5 1.5 0 00-1.5-1.5h-6A1.5 1.5 0 008.25 6v12.75z" />
                                </svg>
                            </div>
                            Real-time Tracking
                        </dt>
                        <dd class="mt-2 text-base leading-7 text-gray-600">
                            Track your order status in real-time from confirmation to delivery.
                        </dd>
                    </div>
                    <div class="relative pl-16">
                        <dt class="text-base font-semibold leading-7 text-gray-900">
                            <div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                                </svg>
                            </div>
                            Flexible Delivery
                        </dt>
                        <dd class="mt-2 text-base leading-7 text-gray-600">
                            Choose between home delivery or pickup at our station in Tambulig.
                        </dd>
                    </div>
                    <div class="relative pl-16">
                        <dt class="text-base font-semibold leading-7 text-gray-900">
                            <div class="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600">
                                <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 006 3.75v16.5a2.25 2.25 0 002.25 2.25h7.5A2.25 2.25 0 0018 20.25V3.75a2.25 2.25 0 00-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
                                </svg>
                            </div>
                            Mobile Friendly
                        </dt>
                        <dd class="mt-2 text-base leading-7 text-gray-600">
                            Optimized for mobile devices, perfect for rural areas with varying internet speeds.
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
</div>
{% endblock %}
