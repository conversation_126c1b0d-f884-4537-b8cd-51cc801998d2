<div class="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Order Placed Successfully!</h3>
            <div class="mt-2 text-sm text-green-700">
                <p>Your order #{{ order.id }} has been placed successfully.</p>
                <div class="mt-2">
                    <p><strong>Product:</strong> {{ order.product.name }}</p>
                    <p><strong>Quantity:</strong> {{ order.quantity }}</p>
                    <p><strong>Total:</strong> ₱{{ order.total_amount }}</p>
                    <p><strong>Delivery Type:</strong> {{ order.get_delivery_type_display }}</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="-mx-2 -my-1.5 flex">
                    <a href="{% url 'core:my_orders' %}" class="bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100">
                        View All Orders
                    </a>
                    <a href="{% url 'core:place_order' %}" class="ml-3 bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100">
                        Place Another Order
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
