/* Prycegas Custom Styles */

/* Loading animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom button styles */
.btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
}

.btn-secondary {
    @apply bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
}

.btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
}

.btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
}

/* Card styles */
.card {
    @apply bg-white shadow-lg rounded-lg overflow-hidden;
}

.card-header {
    @apply px-6 py-4 bg-gray-50 border-b border-gray-200;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
}

/* Status badges */
.status-badge {
    @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
}

.status-pending {
    @apply bg-gray-100 text-gray-800;
}

.status-confirmed {
    @apply bg-blue-100 text-blue-800;
}

.status-out-for-delivery {
    @apply bg-yellow-100 text-yellow-800;
}

.status-delivered {
    @apply bg-green-100 text-green-800;
}

.status-cancelled {
    @apply bg-red-100 text-red-800;
}

/* Form styles */
.form-input {
    @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-select {
    @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
    @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-label {
    @apply block text-sm font-medium text-gray-700;
}

.form-error {
    @apply text-red-600 text-sm mt-1;
}

/* Alert styles */
.alert {
    @apply p-4 rounded-md mb-4;
}

.alert-success {
    @apply bg-green-100 border border-green-400 text-green-700;
}

.alert-error {
    @apply bg-red-100 border border-red-400 text-red-700;
}

.alert-warning {
    @apply bg-yellow-100 border border-yellow-400 text-yellow-700;
}

.alert-info {
    @apply bg-blue-100 border border-blue-400 text-blue-700;
}

/* Responsive optimizations */

/* Mobile devices (320px - 767px) */
@media (max-width: 767px) {
    .mobile-padding {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-full-width {
        width: 100%;
    }

    /* Stack form elements on mobile */
    .form-grid-mobile {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    /* Smaller buttons on mobile */
    .btn-mobile {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    /* Compact cards on mobile */
    .card-mobile {
        padding: 0.75rem;
    }

    /* Smaller text on mobile */
    .text-mobile-sm {
        font-size: 0.75rem;
    }
}

/* Tablet devices (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .form-grid-mobile {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .tablet-grid-2 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .tablet-grid-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

/* Desktop screens (1024px - 1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
    .form-grid-mobile {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .desktop-grid-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .desktop-grid-4 {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

/* Large desktop screens (1440px+) */
@media (min-width: 1440px) {
    .form-grid-mobile {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .large-desktop-grid-4 {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }

    .large-desktop-grid-5 {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 2rem;
    }

    /* Larger text on large screens */
    .text-large-desktop {
        font-size: 1.125rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-full-width {
        width: 100% !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .shadow, .shadow-lg, .shadow-md {
        box-shadow: none !important;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bg-blue-600 {
        background-color: #1E40AF;
    }
    
    .text-gray-600 {
        color: #374151;
    }
    
    .border-gray-300 {
        border-color: #6B7280;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
    .dark-mode-bg {
        background-color: #1F2937;
        color: #F9FAFB;
    }
    
    .dark-mode-card {
        background-color: #374151;
        border-color: #4B5563;
    }
}

/* Custom scrollbar for webkit browsers */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #F3F4F6;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #D1D5DB;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
}

/* Loading skeleton */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Utility classes */
.text-balance {
    text-wrap: balance;
}

.text-pretty {
    text-wrap: pretty;
}
