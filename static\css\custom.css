/* Prycegas Custom Styles - Orange & Black Theme */
/* Professional LPG Distribution System UI */

/* CSS Custom Properties for Orange-Black Theme - Enhanced Contrast */
:root {
    --primary-orange: #FF6B35;
    --primary-orange-hover: #E55A2B;
    --primary-orange-light: #FF8A5C;
    --primary-orange-dark: #CC5429;
    --primary-orange-bright: #FF7A45;
    --background-dark: #0F0F0F;
    --background-dark-secondary: #1A1A1A;
    --background-dark-tertiary: #2A2A2A;
    --background-card: #1F1F1F;
    --text-light: #FFFFFF;
    --text-secondary: #E5E5E5;
    --text-gray: #CCCCCC;
    --text-muted: #999999;
    --border-dark: #333333;
    --border-light: #444444;
    --shadow-dark: rgba(0, 0, 0, 0.5);
    --shadow-orange: rgba(255, 107, 53, 0.3);
    --energy-gradient: linear-gradient(135deg, #FF6B35 0%, #FF8A5C 100%);
    --energy-gradient-hover: linear-gradient(135deg, #E55A2B 0%, #FF7A45 100%);
    --professional-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --glow-orange: 0 0 20px rgba(255, 107, 53, 0.4);
}

/* Typography for Energy and Professionalism */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.01em;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

/* Enhanced Typography with Better Contrast */
.text-energy {
    color: var(--primary-orange);
    font-weight: 800;
    text-shadow: 0 0 30px rgba(255, 107, 53, 0.5);
    position: relative;
}

.text-energy::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--energy-gradient);
    border-radius: 2px;
    opacity: 0.8;
}

.text-gradient {
    background: var(--energy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    /* Fallback for browsers that don't support background-clip */
    color: var(--primary-orange);
}

/* Ensure gradient text has fallback */
@supports not (-webkit-background-clip: text) {
    .text-gradient {
        color: var(--primary-orange) !important;
        -webkit-text-fill-color: unset !important;
    }
}

.professional-text {
    font-weight: 500;
    letter-spacing: -0.01em;
    line-height: 1.6;
    color: var(--text-secondary);
}

.text-high-contrast {
    color: var(--text-light);
    font-weight: 600;
}

.text-medium-contrast {
    color: var(--text-secondary);
    font-weight: 500;
}

.text-low-contrast {
    color: var(--text-gray);
    font-weight: 400;
}

/* Loading animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom button styles with Orange-Black theme */
.btn-primary {
    @apply bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
    background-color: var(--primary-orange);
}

.btn-primary:hover {
    background-color: var(--primary-orange-hover);
}

.btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
    background-color: var(--background-dark-secondary);
}

.btn-secondary:hover {
    background-color: var(--background-dark-tertiary);
}

.btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
}

.btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200;
}

/* Card styles with Orange-Black theme */
.card {
    @apply shadow-lg rounded-lg overflow-hidden;
    background-color: var(--background-dark-secondary);
    border: 1px solid var(--border-dark);
}

.card-header {
    @apply px-6 py-4 border-b;
    background-color: var(--background-dark-tertiary);
    border-color: var(--border-dark);
    color: var(--text-light);
}

.card-body {
    @apply p-6;
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
}

.card-footer {
    @apply px-6 py-4 border-t;
    background-color: var(--background-dark-tertiary);
    border-color: var(--border-dark);
    color: var(--text-light);
}

/* Status badges with Orange-Black theme */
.status-badge {
    @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
}

.status-pending {
    @apply bg-gray-600 text-gray-100;
}

.status-confirmed {
    background-color: var(--primary-orange);
    color: white;
}

.status-out-for-delivery {
    @apply bg-yellow-600 text-yellow-100;
}

.status-delivered {
    @apply bg-green-600 text-green-100;
}

.status-cancelled {
    @apply bg-red-600 text-red-100;
}

/* Form styles with Orange-Black theme */
.form-input {
    @apply mt-1 block w-full px-3 py-2 rounded-md shadow-sm focus:outline-none transition-colors duration-200;
    border: 1px solid var(--border-dark);
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
}

.form-input:focus {
    border-color: var(--primary-orange);
    box-shadow: 0 0 0 1px var(--primary-orange);
}

.form-select {
    @apply mt-1 block w-full px-3 py-2 rounded-md shadow-sm focus:outline-none transition-colors duration-200;
    border: 1px solid var(--border-dark);
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
}

.form-select:focus {
    border-color: var(--primary-orange);
    box-shadow: 0 0 0 1px var(--primary-orange);
}

.form-textarea {
    @apply mt-1 block w-full px-3 py-2 rounded-md shadow-sm focus:outline-none transition-colors duration-200;
    border: 1px solid var(--border-dark);
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
}

.form-textarea:focus {
    border-color: var(--primary-orange);
    box-shadow: 0 0 0 1px var(--primary-orange);
}

.form-label {
    @apply block text-sm font-medium;
    color: var(--text-light);
}

.form-error {
    @apply text-red-400 text-sm mt-1;
}

/* Sidebar Navigation Styles */
.sidebar {
    background-color: var(--background-dark);
    border-right: 1px solid var(--border-dark);
    transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
}

.sidebar-collapsed {
    transform: translateX(-100%);
}

@media (min-width: 768px) {
    .sidebar-collapsed {
        transform: translateX(0);
        width: 4rem;
    }
}

.sidebar-nav-item {
    @apply flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200;
    color: var(--text-gray);
}

.sidebar-nav-item:hover {
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
}

.sidebar-nav-item.active {
    background-color: var(--primary-orange);
    color: white;
}

.sidebar-nav-icon {
    @apply w-5 h-5 mr-3 flex-shrink-0;
}

.sidebar-collapsed .sidebar-nav-icon {
    @apply mr-0;
}

.sidebar-nav-text {
    transition: opacity 0.3s ease-in-out;
}

.sidebar-collapsed .sidebar-nav-text {
    @apply opacity-0;
}

.sidebar-toggle {
    @apply p-2 rounded-lg transition-colors duration-200;
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
}

.sidebar-toggle:hover {
    background-color: var(--background-dark-tertiary);
}

.main-content {
    transition: margin-left 0.3s ease-in-out;
}

.main-content-shifted {
    margin-left: 16rem;
}

.main-content-collapsed {
    margin-left: 4rem;
}

@media (max-width: 767px) {
    .main-content-shifted,
    .main-content-collapsed {
        margin-left: 0;
    }

    .sidebar {
        position: fixed;
        z-index: 50;
        height: 100vh;
    }

    .sidebar-overlay {
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
    }
}

/* Mobile-first responsive breakpoints */
@media (min-width: 320px) {
    .sidebar-nav-item {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

@media (min-width: 640px) {
    .sidebar-nav-item {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

@media (min-width: 768px) {
    .sidebar {
        position: fixed;
        z-index: 30;
    }

    .sidebar-nav-item {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

@media (min-width: 1024px) {
    .sidebar-nav-item {
        padding: 0.875rem 1rem;
        font-size: 0.9375rem;
    }
}

@media (min-width: 1280px) {
    .sidebar-nav-item {
        padding: 1rem;
        font-size: 1rem;
    }
}

@media (min-width: 1920px) {
    .sidebar-nav-item {
        padding: 1.125rem 1rem;
        font-size: 1.0625rem;
    }
}

/* Enhanced Animations and Transitions */
@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToLeft {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

@keyframes scaleIn {
    0% {
        transform: scale(0.95);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Enhanced hover effects */
.sidebar-nav-item {
    position: relative;
    overflow: hidden;
}

.sidebar-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
    transition: left 0.5s ease-in-out;
}

.sidebar-nav-item:hover::before {
    left: 100%;
}

/* Card hover effects */
.card {
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-orange), var(--primary-orange-light));
    transform: scaleX(0);
    transition: transform 0.3s ease-in-out;
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px var(--shadow-dark);
}

/* Button animations */
.btn-primary,
.sidebar-toggle,
[style*="background-color: var(--primary-orange)"] {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
}

.btn-primary::before,
.sidebar-toggle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease-in-out, height 0.3s ease-in-out;
}

.btn-primary:hover::before,
.sidebar-toggle:hover::before {
    width: 300px;
    height: 300px;
}

/* Loading animations */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus animations */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    transform: scale(1.02);
    transition: all 0.2s ease-in-out;
}

/* Status badge animations */
.status-badge {
    transition: all 0.2s ease-in-out;
}

.status-badge:hover {
    transform: scale(1.05);
}

/* Notification animations */
.alert {
    animation: scaleIn 0.3s ease-out;
}

/* Touch and Mobile Optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Touch device optimizations */
    .sidebar-nav-item {
        min-height: 44px; /* iOS recommended touch target size */
        padding: 0.875rem 1rem;
    }

    .btn-primary,
    .sidebar-toggle,
    button {
        min-height: 44px;
        min-width: 44px;
    }

    /* Remove hover effects on touch devices */
    .sidebar-nav-item:hover::before,
    .card:hover::before,
    .btn-primary:hover::before,
    .sidebar-toggle:hover::before {
        display: none;
    }

    /* Enhance tap feedback */
    .sidebar-nav-item:active {
        background-color: var(--primary-orange);
        color: white;
        transform: scale(0.98);
    }

    .card:active {
        transform: scale(0.98);
    }
}

/* Ultra-wide screen optimizations */
@media (min-width: 2560px) {
    .sidebar {
        width: 20rem;
    }

    .main-content-shifted {
        margin-left: 20rem;
    }

    .sidebar-nav-item {
        padding: 1.25rem 1.5rem;
        font-size: 1.125rem;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .sidebar-nav-icon,
    svg {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape mobile optimizations */
@media (max-height: 500px) and (orientation: landscape) {
    .sidebar {
        width: 12rem;
    }

    .sidebar-nav-item {
        padding: 0.5rem 0.75rem;
        font-size: 0.8125rem;
    }

    .sidebar-nav-icon {
        width: 1rem;
        height: 1rem;
        margin-right: 0.5rem;
    }
}

/* Print optimizations */
@media print {
    .sidebar,
    .sidebar-toggle,
    .mobile-overlay {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding-top: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    * {
        color: black !important;
        background: white !important;
    }
}

/* Responsive animation adjustments */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
.sidebar-nav-item:focus,
.btn-primary:focus,
.sidebar-toggle:focus,
button:focus,
a:focus {
    outline: 2px solid var(--primary-orange);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-orange: #FF4500;
        --background-dark: #000000;
        --background-dark-secondary: #1A1A1A;
        --text-light: #FFFFFF;
        --border-dark: #666666;
    }

    .card,
    .sidebar {
        border: 2px solid var(--border-dark);
    }
}

/* Professional Energy Elements */
.energy-indicator {
    position: relative;
    overflow: hidden;
}

.energy-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: var(--energy-gradient);
    animation: energyFlow 2s ease-in-out infinite;
}

@keyframes energyFlow {
    0% {
        left: -100%;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

/* Urgency Indicators */
.urgent {
    position: relative;
    animation: urgentPulse 2s ease-in-out infinite;
}

@keyframes urgentPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(255, 107, 53, 0);
    }
}

/* Professional Status Indicators */
.status-professional {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: var(--energy-gradient);
    color: white;
    box-shadow: var(--professional-shadow);
}

.status-pending {
    background-color: var(--background-dark-tertiary);
    color: var(--text-gray);
    border: 1px solid var(--border-dark);
}

/* LPG Industry Specific Elements */
.lpg-tank-icon {
    filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
}

.delivery-route {
    background: linear-gradient(90deg,
        var(--primary-orange) 0%,
        var(--primary-orange-light) 50%,
        var(--primary-orange) 100%);
    height: 2px;
    position: relative;
    overflow: hidden;
}

.delivery-route::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%);
    animation: deliveryProgress 3s ease-in-out infinite;
}

@keyframes deliveryProgress {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Emergency/Critical Styling */
.critical {
    background: linear-gradient(135deg, #FF4444, #CC0000);
    color: white;
    font-weight: 600;
    animation: criticalAlert 1s ease-in-out infinite alternate;
}

@keyframes criticalAlert {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0.8;
    }
}

/* Professional Data Visualization */
.data-bar {
    height: 8px;
    background-color: var(--background-dark-tertiary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.data-bar-fill {
    height: 100%;
    background: var(--energy-gradient);
    border-radius: 4px;
    transition: width 1s ease-out;
    position: relative;
}

.data-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    animation: dataShimmer 2s ease-in-out infinite;
}

@keyframes dataShimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Enhanced Micro-Interactions */
.micro-bounce {
    transition: transform 0.2s ease-in-out;
}

.micro-bounce:hover {
    transform: translateY(-2px);
}

.micro-bounce:active {
    transform: translateY(0px) scale(0.98);
}

.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
    width: 300px;
    height: 300px;
}

/* Page Transition Effects */
.page-enter {
    animation: pageEnter 0.5s ease-out;
}

.page-exit {
    animation: pageExit 0.3s ease-in;
}

@keyframes pageEnter {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pageExit {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px);
    }
}

/* Enhanced Loading States */
.skeleton {
    background: linear-gradient(90deg, var(--background-dark-secondary) 25%, var(--background-dark-tertiary) 50%, var(--background-dark-secondary) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.6);
    }
}

/* Interactive Form Elements */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-input-enhanced {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-dark);
    border-radius: 0.5rem;
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
    font-size: 1rem;
    transition: all 0.3s ease-in-out;
    outline: none;
}

.form-input-enhanced:focus {
    border-color: var(--primary-orange);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    transform: translateY(-1px);
}

.form-input-enhanced:focus + .form-label-floating {
    transform: translateY(-1.5rem) scale(0.85);
    color: var(--primary-orange);
}

.form-label-floating {
    position: absolute;
    left: 1rem;
    top: 0.75rem;
    color: var(--text-gray);
    font-size: 1rem;
    transition: all 0.3s ease-in-out;
    pointer-events: none;
    background-color: var(--background-dark-secondary);
    padding: 0 0.25rem;
}

.form-input-enhanced:not(:placeholder-shown) + .form-label-floating {
    transform: translateY(-1.5rem) scale(0.85);
    color: var(--primary-orange);
}

/* Enhanced Button States */
.btn-enhanced {
    position: relative;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease-in-out;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-enhanced:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary-enhanced {
    background: var(--energy-gradient);
    color: white;
}

.btn-secondary-enhanced {
    background-color: var(--background-dark-secondary);
    color: var(--text-light);
    border: 2px solid var(--border-dark);
}

.btn-secondary-enhanced:hover {
    border-color: var(--primary-orange);
    background-color: var(--background-dark-tertiary);
}

/* Notification Enhancements */
.notification-slide-in {
    animation: notificationSlideIn 0.4s ease-out;
}

.notification-slide-out {
    animation: notificationSlideOut 0.3s ease-in;
}

@keyframes notificationSlideIn {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes notificationSlideOut {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Progress Indicators */
.progress-ring {
    width: 60px;
    height: 60px;
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: var(--background-dark-tertiary);
    stroke-width: 4;
    stroke-linecap: round;
    transition: stroke-dasharray 0.5s ease-in-out;
}

.progress-ring-progress {
    stroke: var(--primary-orange);
    stroke-dasharray: 0 188.5;
    transition: stroke-dasharray 0.5s ease-in-out;
}

/* Tooltip Enhancements */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--background-dark-tertiary);
    color: var(--text-light);
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
    z-index: 1000;
    border: 1px solid var(--border-dark);
}

.tooltip::before {
    content: '';
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--background-dark-tertiary);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.tooltip:hover::after,
.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Scroll-triggered Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Card Interactions */
.interactive-card {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}

.interactive-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.interactive-card:active {
    transform: translateY(-2px) scale(1.01);
}

/* Loading Button State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Landing Page Specific Enhancements */
.hero-text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.enhanced-readability {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    font-weight: 600;
}

/* Fix for gradient text fallback */
.text-gradient-safe {
    background: var(--energy-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

/* Fallback for unsupported browsers */
@supports not (-webkit-background-clip: text) {
    .text-gradient-safe {
        color: var(--primary-orange) !important;
        background: none !important;
        -webkit-text-fill-color: unset !important;
    }
}

/* Enhanced contrast for all text elements */
body {
    color: var(--text-light);
    background-color: var(--background-dark);
}

/* Ensure proper contrast ratios */
.contrast-fix {
    color: var(--text-light) !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
}

.contrast-medium {
    color: var(--text-secondary) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.contrast-low {
    color: var(--text-gray) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Interactive elements enhancement */
.interactive-glow:hover {
    box-shadow: var(--glow-orange);
    transform: translateY(-2px);
}

/* Card enhancements */
.enhanced-card {
    background: var(--background-card);
    border: 2px solid var(--border-dark);
    box-shadow: var(--professional-shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-card:hover {
    border-color: var(--primary-orange);
    box-shadow: var(--glow-orange), var(--professional-shadow);
    transform: translateY(-4px);
}

/* Button state improvements */
.btn-state-loading {
    opacity: 0.8;
    cursor: not-allowed;
    transform: none !important;
}

.btn-state-success {
    background: linear-gradient(135deg, #10B981, #059669) !important;
    color: white !important;
}

.btn-state-error {
    background: linear-gradient(135deg, #EF4444, #DC2626) !important;
    color: white !important;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-glow,
    .patternFloat {
        animation: none !important;
    }
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
    :root {
        --text-light: #FFFFFF;
        --text-secondary: #F0F0F0;
        --text-gray: #E0E0E0;
        --primary-orange: #FF8A5C;
        --border-dark: #666666;
    }

    .text-energy,
    .text-gradient,
    .text-gradient-safe {
        color: var(--primary-orange) !important;
        background: none !important;
        -webkit-text-fill-color: unset !important;
        text-shadow: 0 0 10px rgba(255, 138, 92, 0.8);
    }
}

/* Alert styles with Orange-Black theme */
.alert {
    @apply p-4 rounded-md mb-4;
    background-color: var(--background-dark-secondary);
    border: 1px solid var(--border-dark);
}

.alert-success {
    @apply bg-green-800 border border-green-600 text-green-100;
}

.alert-error {
    @apply bg-red-800 border border-red-600 text-red-100;
}

.alert-warning {
    @apply bg-yellow-800 border border-yellow-600 text-yellow-100;
}

.alert-info {
    background-color: var(--primary-orange);
    border-color: var(--primary-orange-dark);
    color: white;
}

/* Responsive optimizations */

/* Mobile devices (320px - 767px) */
@media (max-width: 767px) {
    .mobile-padding {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .mobile-text-sm {
        font-size: 0.875rem;
    }

    .mobile-hidden {
        display: none;
    }

    .mobile-full-width {
        width: 100%;
    }

    /* Stack form elements on mobile */
    .form-grid-mobile {
        display: grid;
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    /* Smaller buttons on mobile */
    .btn-mobile {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    /* Compact cards on mobile */
    .card-mobile {
        padding: 0.75rem;
    }

    /* Smaller text on mobile */
    .text-mobile-sm {
        font-size: 0.75rem;
    }
}

/* Tablet devices (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .form-grid-mobile {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .tablet-grid-2 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .tablet-grid-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

/* Desktop screens (1024px - 1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
    .form-grid-mobile {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .desktop-grid-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .desktop-grid-4 {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

/* Large desktop screens (1440px+) */
@media (min-width: 1440px) {
    .form-grid-mobile {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .large-desktop-grid-4 {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }

    .large-desktop-grid-5 {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 2rem;
    }

    /* Larger text on large screens */
    .text-large-desktop {
        font-size: 1.125rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-full-width {
        width: 100% !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .shadow, .shadow-lg, .shadow-md {
        box-shadow: none !important;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .bg-blue-600 {
        background-color: #1E40AF;
    }
    
    .text-gray-600 {
        color: #374151;
    }
    
    .border-gray-300 {
        border-color: #6B7280;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
    .dark-mode-bg {
        background-color: #1F2937;
        color: #F9FAFB;
    }
    
    .dark-mode-card {
        background-color: #374151;
        border-color: #4B5563;
    }
}

/* Custom scrollbar for webkit browsers */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #F3F4F6;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #D1D5DB;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
}

/* Loading skeleton */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Utility classes */
.text-balance {
    text-wrap: balance;
}

.text-pretty {
    text-wrap: pretty;
}
