{% extends 'base.html' %}

{% block title %}Reports - Prycegas{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Business Reports</h1>
            <p class="mt-2 text-sm text-gray-700">View sales, inventory, and delivery reports for your business.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <a href="{% url 'core:admin_dashboard' %}" class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Date Range Filter -->
    <div class="mt-4 sm:mt-6 bg-white shadow-sm sm:shadow rounded-lg p-3 sm:p-4">
        <div class="flex flex-col sm:flex-row sm:flex-wrap items-start sm:items-center gap-3 sm:gap-4">
            <div class="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                <label for="start-date" class="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">From:</label>
                <input type="date"
                       id="start-date"
                       name="start_date"
                       value="{{ start_date|date:'Y-m-d' }}"
                       hx-get="{% url 'core:reports' %}"
                       hx-target="#reports-container"
                       hx-include="[name='start_date'], [name='end_date']"
                       hx-trigger="change"
                       class="w-full sm:w-auto rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-xs sm:text-sm">
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                <label for="end-date" class="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">To:</label>
                <input type="date"
                       id="end-date"
                       name="end_date"
                       value="{{ end_date|date:'Y-m-d' }}"
                       hx-get="{% url 'core:reports' %}"
                       hx-target="#reports-container"
                       hx-include="[name='start_date'], [name='end_date']"
                       hx-trigger="change"
                       class="w-full sm:w-auto rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-xs sm:text-sm">
            </div>

            <button onclick="window.print()" class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 w-full sm:w-auto">
                <svg class="-ml-0.5 mr-2 h-3 w-3 sm:h-4 sm:w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Print Report
            </button>
        </div>
    </div>
    
    <!-- Reports Content -->
    <div id="reports-container" class="mt-6">
        {% include 'partials/reports_content.html' %}
    </div>
</div>
{% endblock %}
