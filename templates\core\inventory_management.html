{% extends 'base/dashboard.html' %}
{% load math_filters %}

{% block title %}Inventory Management - Prycegas{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Inventory Management</h1>
            <p class="mt-2 text-sm text-gray-700">Monitor stock levels and manage inventory for all LPG products.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-2">
            <a href="{% url 'core:delivery_logs' %}" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700">
                Log New Delivery
            </a>
            <a href="{% url 'core:admin_dashboard' %}" class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Inventory Grid -->
    <div class="mt-6 sm:mt-8 grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
        {% for inventory in inventories %}
        <div class="bg-white overflow-hidden shadow-sm sm:shadow rounded-lg hover:shadow-md transition-shadow duration-200">
            <div class="p-4 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-3 h-3 sm:w-5 sm:h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 sm:ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-xs sm:text-sm font-medium text-gray-500 truncate">{{ inventory.product.name }}</dt>
                            <dd class="flex items-baseline">
                                <div class="text-lg sm:text-2xl font-semibold text-gray-900">{{ inventory.current_stock }}</div>
                                <div class="ml-1 sm:ml-2 text-xs sm:text-sm text-gray-500">units</div>
                            </dd>
                        </dl>
                    </div>
                </div>
                
                <div class="mt-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">Minimum Stock:</span>
                        <span class="font-medium">{{ inventory.minimum_stock }}</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-gray-500">Price per Unit:</span>
                        <span class="font-medium">₱{{ inventory.product.price }}</span>
                    </div>
                    <div class="flex items-center justify-between text-sm mt-1">
                        <span class="text-gray-500">Last Updated:</span>
                        <span class="font-medium">{{ inventory.last_updated|date:"M d, Y" }}</span>
                    </div>
                </div>
                
                <!-- Stock Status -->
                <div class="mt-4">
                    {% if inventory.is_low_stock %}
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-medium text-red-600">Low Stock Alert</span>
                        </div>
                        <div class="mt-2 bg-red-50 border border-red-200 rounded-md p-3">
                            <p class="text-sm text-red-700">
                                Stock is running low. Consider ordering more from your distributor.
                            </p>
                        </div>
                    {% else %}
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-medium text-green-600">Stock OK</span>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Stock Level Progress Bar -->
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Stock Level</span>
                        <span>{{ inventory.current_stock }}/{{ inventory.minimum_stock|add:inventory.current_stock }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        {% with stock_percentage=inventory.current_stock|floatformat:0 min_stock=inventory.minimum_stock|floatformat:0 %}
                            {% with total_capacity=min_stock|add:stock_percentage %}
                                {% with percentage=stock_percentage|div:total_capacity|mul:100 %}
                                    <div class="h-2 rounded-full {% if inventory.is_low_stock %}bg-red-500{% else %}bg-green-500{% endif %}" 
                                         style="width: {% if percentage > 100 %}100{% else %}{{ percentage|floatformat:0 }}{% endif %}%"></div>
                                {% endwith %}
                            {% endwith %}
                        {% endwith %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full">
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No inventory items</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by adding some products to your inventory.</p>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- Summary Statistics -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Inventory Summary</h3>
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-3">
                <div class="bg-blue-50 overflow-hidden rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-blue-600 truncate">Total Products</dt>
                                    <dd class="text-lg font-medium text-blue-900">{{ inventories.count }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-red-50 overflow-hidden rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-red-600 truncate">Low Stock Items</dt>
                                    <dd class="text-lg font-medium text-red-900">
                                        {% for inventory in inventories %}
                                            {% if inventory.is_low_stock %}{{ forloop.counter }}{% endif %}
                                        {% empty %}0{% endfor %}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 overflow-hidden rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-green-600 truncate">Well Stocked</dt>
                                    <dd class="text-lg font-medium text-green-900">
                                        {% for inventory in inventories %}
                                            {% if not inventory.is_low_stock %}{{ forloop.counter }}{% endif %}
                                        {% empty %}0{% endfor %}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
