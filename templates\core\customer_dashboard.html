{% extends 'base.html' %}

{% block title %}Dashboard - Prycegas{% endblock %}

{% block content %}
<div class="px-0 sm:px-0">
    <!-- Welcome section -->
    <div class="mb-6 sm:mb-8 px-4 sm:px-0">
        <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">Welcome, {{ customer.user.get_full_name|default:customer.user.username }}!</h1>
        <p class="mt-2 text-sm sm:text-base text-gray-600">Manage your LPG orders and account from here.</p>
    </div>

    <!-- Quick actions -->
    <div class="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 xl:grid-cols-3 mb-6 sm:mb-8 px-4 sm:px-0">
        <div class="bg-white overflow-hidden shadow-sm sm:shadow rounded-lg hover:shadow-md transition-shadow duration-200">
            <div class="p-4 sm:p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
                        </svg>
                    </div>
                    <div class="ml-3 sm:ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-xs sm:text-sm font-medium text-gray-500 truncate">Place New Order</dt>
                            <dd class="text-base sm:text-lg font-medium text-gray-900">Quick & Easy</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-3 sm:mt-4">
                    <a href="{% url 'core:place_order' %}" class="text-xs sm:text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
                        Order Now →
                    </a>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 01-3 0V6a3 3 0 013-3h6a3 3 0 013 3v7.5a1.5 1.5 0 01-3 0V6a1.5 1.5 0 00-1.5-1.5h-6A1.5 1.5 0 008.25 6v12.75z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Order History</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ recent_orders|length }} Recent</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'core:my_orders' %}" class="text-sm font-medium text-green-600 hover:text-green-500">
                        View All →
                    </a>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Profile</dt>
                            <dd class="text-lg font-medium text-gray-900">Manage Account</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{% url 'auth:profile' %}" class="text-sm font-medium text-purple-600 hover:text-purple-500">
                        View Profile →
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent orders -->
    <div class="bg-white shadow-sm sm:shadow overflow-hidden sm:rounded-md mb-6 sm:mb-8 mx-4 sm:mx-0">
        <div class="px-4 py-4 sm:px-6 sm:py-5">
            <h3 class="text-base sm:text-lg leading-6 font-medium text-gray-900">Recent Orders</h3>
            <p class="mt-1 max-w-2xl text-xs sm:text-sm text-gray-500">Your latest LPG orders and their status.</p>
        </div>
        <ul class="divide-y divide-gray-200">
            {% for order in recent_orders %}
            <li>
                <div class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-blue-600 truncate">
                                Order #{{ order.id }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                   {% if order.status == 'delivered' %}bg-green-100 text-green-800
                                   {% elif order.status == 'out_for_delivery' %}bg-yellow-100 text-yellow-800
                                   {% elif order.status == 'confirmed' %}bg-blue-100 text-blue-800
                                   {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                   {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ order.get_status_display }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <p>{{ order.created_at|date:"M d, Y" }}</p>
                        </div>
                    </div>
                    <div class="mt-2 sm:flex sm:justify-between">
                        <div class="sm:flex">
                            <p class="flex items-center text-sm text-gray-500">
                                {{ order.product.name }} x{{ order.quantity }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                ₱{{ order.total_amount }}
                            </p>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                            <a href="{% url 'core:order_detail' order.id %}" class="font-medium text-blue-600 hover:text-blue-500">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </li>
            {% empty %}
            <li class="px-4 py-4 sm:px-6">
                <p class="text-sm text-gray-500">No orders yet. <a href="{% url 'core:place_order' %}" class="text-blue-600 hover:text-blue-500">Place your first order</a>.</p>
            </li>
            {% endfor %}
        </ul>
    </div>
    
    <!-- Available products -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Available Products</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Current LPG products and prices.</p>
        </div>
        <div class="border-t border-gray-200">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 p-6">
                {% for product in products %}
                <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">{{ product.name }}</p>
                        <p class="text-sm text-gray-500">{{ product.weight_kg }}kg</p>
                        <p class="text-lg font-bold text-blue-600">₱{{ product.price }}</p>
                    </div>
                    <div>
                        <a href="{% url 'core:place_order' %}?product={{ product.id }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Order
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
