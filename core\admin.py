from django.contrib import admin
from .models import Customer, LPGProduct, Inventory, Order, DeliveryLog, OrderStatusHistory


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone_number', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'user__email', 'phone_number']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(LPGProduct)
class LPGProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'weight_kg', 'price', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name']
    readonly_fields = ['created_at']


@admin.register(Inventory)
class InventoryAdmin(admin.ModelAdmin):
    list_display = ['product', 'current_stock', 'minimum_stock', 'is_low_stock', 'last_updated']
    list_filter = ['last_updated']
    search_fields = ['product__name']
    readonly_fields = ['last_updated']

    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.boolean = True
    is_low_stock.short_description = 'Low Stock'


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'customer', 'product', 'quantity', 'status', 'delivery_type', 'total_amount', 'created_at']
    list_filter = ['status', 'delivery_type', 'created_at']
    search_fields = ['customer__user__username', 'product__name']
    readonly_fields = ['total_amount', 'created_at', 'updated_at', 'delivered_at']

    fieldsets = (
        ('Order Information', {
            'fields': ('customer', 'product', 'quantity', 'delivery_type', 'delivery_address')
        }),
        ('Status & Payment', {
            'fields': ('status', 'total_amount', 'notes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'delivered_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(DeliveryLog)
class DeliveryLogAdmin(admin.ModelAdmin):
    list_display = ['product', 'quantity_received', 'supplier_name', 'delivery_date', 'total_cost', 'created_by']
    list_filter = ['delivery_date', 'supplier_name', 'created_by']
    search_fields = ['product__name', 'supplier_name', 'invoice_number']
    readonly_fields = ['total_cost', 'created_at']


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ['order', 'old_status', 'new_status', 'changed_by', 'changed_at']
    list_filter = ['old_status', 'new_status', 'changed_at']
    search_fields = ['order__id', 'changed_by__username']
    readonly_fields = ['changed_at']
