
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone


class Customer(models.Model):
    """Extended user profile for customers"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=15, blank=True)
    address = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.phone_number}"

    class Meta:
        ordering = ['-created_at']


class LPGProduct(models.Model):
    """LPG product types (11kg, 22kg, etc.)"""
    name = models.CharField(max_length=100)  # e.g., "11kg LPG Tank"
    weight_kg = models.DecimalField(max_digits=5, decimal_places=2)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} - ₱{self.price}"

    class Meta:
        ordering = ['weight_kg']


class Inventory(models.Model):
    """Current stock levels for each LPG product"""
    product = models.OneToOneField(LPGProduct, on_delete=models.CASCADE)
    current_stock = models.PositiveIntegerField(default=0)
    minimum_stock = models.PositiveIntegerField(default=10)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.product.name} - Stock: {self.current_stock}"

    @property
    def is_low_stock(self):
        return self.current_stock <= self.minimum_stock

    class Meta:
        verbose_name_plural = "Inventories"


class Order(models.Model):
    """Customer orders for LPG products"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('out_for_delivery', 'Out for Delivery'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    ]

    DELIVERY_TYPE_CHOICES = [
        ('pickup', 'Pickup'),
        ('delivery', 'Delivery'),
    ]

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='orders')
    product = models.ForeignKey(LPGProduct, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    delivery_type = models.CharField(max_length=10, choices=DELIVERY_TYPE_CHOICES)
    delivery_address = models.TextField(blank=True)  # Required if delivery_type is 'delivery'
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    notes = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    delivered_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Order #{self.id} - {self.customer.user.username} - {self.product.name} x{self.quantity}"

    def save(self, *args, **kwargs):
        # Auto-calculate total amount
        self.total_amount = self.product.price * self.quantity

        # Set delivered_at when status changes to delivered
        if self.status == 'delivered' and not self.delivered_at:
            self.delivered_at = timezone.now()

        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['-created_at']),
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['status']),
        ]


class DeliveryLog(models.Model):
    """Log of stock deliveries from distributor to dealer"""
    product = models.ForeignKey(LPGProduct, on_delete=models.CASCADE)
    quantity_received = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    supplier_name = models.CharField(max_length=200)
    delivery_date = models.DateTimeField(default=timezone.now)
    cost_per_unit = models.DecimalField(max_digits=10, decimal_places=2)
    total_cost = models.DecimalField(max_digits=10, decimal_places=2)
    invoice_number = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Delivery: {self.product.name} x{self.quantity_received} from {self.supplier_name}"

    def save(self, *args, **kwargs):
        # Auto-calculate total cost
        self.total_cost = self.cost_per_unit * self.quantity_received

        # Update inventory when delivery is saved
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            inventory, _ = Inventory.objects.get_or_create(
                product=self.product,
                defaults={'current_stock': 0}
            )
            inventory.current_stock += self.quantity_received
            inventory.save()

    class Meta:
        ordering = ['-delivery_date']
        indexes = [
            models.Index(fields=['-delivery_date']),
            models.Index(fields=['product']),
        ]


class OrderStatusHistory(models.Model):
    """Track order status changes for audit trail"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='status_history')
    old_status = models.CharField(max_length=20, blank=True)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    changed_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    def __str__(self):
        return f"Order #{self.order.id}: {self.old_status} → {self.new_status}"

    class Meta:
        ordering = ['-changed_at']
        verbose_name_plural = "Order Status Histories"
