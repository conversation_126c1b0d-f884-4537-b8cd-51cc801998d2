{% extends 'base/dashboard.html' %}

{% block title %}Orders Management - Prycegas{% endblock %}

{% block content %}
<div class="px-2 sm:px-4 lg:px-6 xl:px-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="flex-auto">
            <h1 class="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900">Orders Management</h1>
            <p class="mt-2 text-sm sm:text-base text-gray-700">Manage all customer orders and update their status.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-4 lg:ml-16 sm:flex-none">
            <a href="{% url 'core:admin_dashboard' %}" class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors duration-200 w-full sm:w-auto">
                Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="mt-4 sm:mt-6 bg-white shadow-sm sm:shadow rounded-lg p-3 sm:p-4">
        <div class="flex flex-col sm:flex-row sm:flex-wrap items-start sm:items-center gap-3 sm:gap-4">
            <div class="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                <label for="status-filter" class="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">Filter by status:</label>
                <select id="status-filter"
                        hx-get="{% url 'core:orders' %}"
                        hx-target="#orders-container"
                        hx-include="[name='status'], [name='search']"
                        name="status"
                        class="w-full sm:w-auto rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-xs sm:text-sm">
                    <option value="">All Orders</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
                <label for="search" class="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">Search:</label>
                <input type="text"
                       id="search"
                       name="search"
                       value="{{ search_query }}"
                       placeholder="Order ID or customer name..."
                       hx-get="{% url 'core:orders' %}"
                       hx-target="#orders-container"
                       hx-include="[name='status'], [name='search']"
                       hx-trigger="keyup changed delay:500ms"
                       class="w-full sm:w-auto rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-xs sm:text-sm">
            </div>
        </div>
    </div>
    
    <!-- Orders list -->
    <div id="orders-container" class="mt-6">
        {% include 'partials/admin_orders_list.html' %}
    </div>
</div>

<!-- Order Status Update Modal -->
<div id="status-update-modal" 
     x-data="{ open: false, orderId: null }" 
     @open-status-modal.window="open = true; orderId = $event.detail.orderId"
     @close-status-modal.window="open = false; orderId = null"
     x-show="open" 
     x-cloak
     class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" @click="open = false">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div id="status-update-content">
                <!-- Modal content will be loaded here by HTMX -->
            </div>
        </div>
    </div>
</div>
{% endblock %}
