<div class="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Delivery Logged Successfully!</h3>
            <div class="mt-2 text-sm text-green-700">
                <p>{{ delivery_log.product.name }} delivery has been logged.</p>
                <div class="mt-2">
                    <p><strong>Quantity:</strong> {{ delivery_log.quantity_received }} units</p>
                    <p><strong>Supplier:</strong> {{ delivery_log.supplier_name }}</p>
                    <p><strong>Total Cost:</strong> ₱{{ delivery_log.total_cost }}</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="-mx-2 -my-1.5 flex">
                    <button @click="location.reload()" class="bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100">
                        Refresh Page
                    </button>
                    <a href="{% url 'core:inventory' %}" class="ml-3 bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100">
                        View Inventory
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
