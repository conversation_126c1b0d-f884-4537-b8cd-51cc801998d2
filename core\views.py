from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.db.models import Q, Sum, Count, F
from django.utils import timezone
from .models import Customer, Order, LPGProduct, Inventory, DeliveryLog
from .forms import OrderForm, DeliveryLogForm, OrderStatusUpdateForm


def home(request):
    """Home page - redirect based on user type"""
    if request.user.is_authenticated:
        if request.user.is_staff:
            return redirect('core:admin_dashboard')
        else:
            return redirect('core:customer_dashboard')
    return render(request, 'core/home.html')


@login_required
def dashboard(request):
    """Generic dashboard redirect"""
    if request.user.is_staff:
        return redirect('core:admin_dashboard')
    else:
        return redirect('core:customer_dashboard')


# Customer Views
@login_required
def customer_dashboard(request):
    """Customer dashboard with recent orders and quick actions"""
    try:
        customer = request.user.customer
    except Customer.DoesNotExist:
        messages.error(request, 'Customer profile not found. Please contact support.')
        return redirect('core:home')

    # Get recent orders
    recent_orders = Order.objects.filter(customer=customer).order_by('-created_at')[:5]

    # Get available products
    products = LPGProduct.objects.filter(is_active=True)

    context = {
        'customer': customer,
        'recent_orders': recent_orders,
        'products': products,
    }
    return render(request, 'core/customer_dashboard.html', context)


@login_required
def place_order(request):
    """Place a new LPG order"""
    if request.user.is_staff:
        return redirect('core:admin_dashboard')

    try:
        customer = request.user.customer
    except Customer.DoesNotExist:
        messages.error(request, 'Customer profile not found. Please contact support.')
        return redirect('core:home')

    if request.method == 'POST':
        form = OrderForm(request.POST)
        if form.is_valid():
            order = form.save(commit=False)
            order.customer = customer
            order.save()

            messages.success(request, f'Order placed successfully! Order #{order.id}')

            if request.htmx:
                return render(request, 'partials/order_success.html', {'order': order})
            return redirect('core:my_orders')
    else:
        form = OrderForm()
        # Pre-fill delivery address with customer's address
        form.fields['delivery_address'].initial = customer.address

    context = {
        'form': form,
        'customer': customer,
    }

    if request.htmx:
        return render(request, 'partials/order_form.html', context)
    return render(request, 'core/place_order.html', context)


@login_required
def my_orders(request):
    """Customer's order history"""
    if request.user.is_staff:
        return redirect('core:admin_dashboard')

    try:
        customer = request.user.customer
    except Customer.DoesNotExist:
        messages.error(request, 'Customer profile not found. Please contact support.')
        return redirect('core:home')

    # Get orders with filtering
    status_filter = request.GET.get('status', '')
    orders = Order.objects.filter(customer=customer)

    if status_filter:
        orders = orders.filter(status=status_filter)

    orders = orders.order_by('-created_at')

    context = {
        'orders': orders,
        'status_filter': status_filter,
        'status_choices': Order.STATUS_CHOICES,
    }

    if request.htmx:
        return render(request, 'partials/orders_list.html', context)
    return render(request, 'core/my_orders.html', context)


@login_required
def order_detail(request, order_id):
    """View order details"""
    try:
        customer = request.user.customer
        order = get_object_or_404(Order, id=order_id, customer=customer)
    except Customer.DoesNotExist:
        messages.error(request, 'Customer profile not found. Please contact support.')
        return redirect('core:home')

    context = {
        'order': order,
    }

    if request.htmx:
        return render(request, 'partials/order_detail.html', context)
    return render(request, 'core/order_detail.html', context)


# Admin/Dealer Views
@login_required
def admin_dashboard(request):
    """Admin/Dealer dashboard with overview statistics"""
    if not request.user.is_staff:
        return redirect('core:customer_dashboard')

    # Get statistics
    total_orders = Order.objects.count()
    pending_orders = Order.objects.filter(status='pending').count()
    delivered_orders = Order.objects.filter(status='delivered').count()

    # Get recent orders
    recent_orders = Order.objects.order_by('-created_at')[:10]

    # Get low stock items
    low_stock_items = Inventory.objects.filter(current_stock__lte=F('minimum_stock'))

    # Get recent deliveries
    recent_deliveries = DeliveryLog.objects.order_by('-delivery_date')[:5]

    context = {
        'total_orders': total_orders,
        'pending_orders': pending_orders,
        'delivered_orders': delivered_orders,
        'recent_orders': recent_orders,
        'low_stock_items': low_stock_items,
        'recent_deliveries': recent_deliveries,
    }
    return render(request, 'core/admin_dashboard.html', context)


@login_required
def orders_management(request):
    """Manage all customer orders"""
    if not request.user.is_staff:
        return redirect('core:customer_dashboard')

    # Get orders with filtering
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')

    orders = Order.objects.select_related('customer__user', 'product').order_by('-created_at')

    if status_filter:
        orders = orders.filter(status=status_filter)

    if search_query:
        orders = orders.filter(
            Q(customer__user__username__icontains=search_query) |
            Q(customer__user__first_name__icontains=search_query) |
            Q(customer__user__last_name__icontains=search_query) |
            Q(id__icontains=search_query)
        )

    context = {
        'orders': orders,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_choices': Order.STATUS_CHOICES,
    }

    if request.htmx:
        return render(request, 'partials/admin_orders_list.html', context)
    return render(request, 'core/orders_management.html', context)


@login_required
@require_http_methods(["POST"])
def update_order_status(request, order_id):
    """Update order status via HTMX"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Permission denied'}, status=403)

    order = get_object_or_404(Order, id=order_id)
    form = OrderStatusUpdateForm(request.POST, instance=order)

    if form.is_valid():
        old_status = order.status
        order = form.save()

        # Create status history record
        from .models import OrderStatusHistory
        OrderStatusHistory.objects.create(
            order=order,
            old_status=old_status,
            new_status=order.status,
            changed_by=request.user,
            notes=form.cleaned_data.get('notes', '')
        )

        messages.success(request, f'Order #{order.id} status updated to {order.get_status_display()}')

        if request.htmx:
            return render(request, 'partials/order_status_updated.html', {'order': order})
        return redirect('core:orders')

    return JsonResponse({'error': 'Invalid form data'}, status=400)


@login_required
def inventory_management(request):
    """Manage inventory levels"""
    if not request.user.is_staff:
        return redirect('core:customer_dashboard')

    inventories = Inventory.objects.select_related('product').order_by('product__name')

    context = {
        'inventories': inventories,
    }

    if request.htmx:
        return render(request, 'partials/inventory_list.html', context)
    return render(request, 'core/inventory_management.html', context)


@login_required
def delivery_logs(request):
    """View and add delivery logs"""
    if not request.user.is_staff:
        return redirect('core:customer_dashboard')

    if request.method == 'POST':
        form = DeliveryLogForm(request.POST)
        if form.is_valid():
            delivery_log = form.save(commit=False)
            delivery_log.created_by = request.user
            delivery_log.save()

            messages.success(request, f'Delivery logged: {delivery_log.product.name} x{delivery_log.quantity_received}')

            if request.htmx:
                return render(request, 'partials/delivery_log_success.html', {'delivery_log': delivery_log})
            return redirect('core:delivery_logs')
    else:
        form = DeliveryLogForm()

    # Get recent delivery logs
    logs = DeliveryLog.objects.select_related('product', 'created_by').order_by('-delivery_date')

    context = {
        'form': form,
        'logs': logs,
    }

    if request.htmx:
        return render(request, 'partials/delivery_logs_content.html', context)
    return render(request, 'core/delivery_logs.html', context)


@login_required
def reports(request):
    """Generate various reports"""
    if not request.user.is_staff:
        return redirect('core:customer_dashboard')

    # Date range filters
    from datetime import timedelta
    from django.utils import timezone

    end_date = timezone.now()
    start_date = end_date - timedelta(days=30)  # Default to last 30 days

    # Get date range from request
    if request.GET.get('start_date'):
        start_date = timezone.datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d')
        start_date = timezone.make_aware(start_date)

    if request.GET.get('end_date'):
        end_date = timezone.datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d')
        end_date = timezone.make_aware(end_date.replace(hour=23, minute=59, second=59))

    # Sales Report
    orders_in_period = Order.objects.filter(
        created_at__range=[start_date, end_date],
        status__in=['confirmed', 'out_for_delivery', 'delivered']
    )

    total_sales = orders_in_period.aggregate(total=Sum('total_amount'))['total'] or 0
    total_orders = orders_in_period.count()
    delivered_orders = orders_in_period.filter(status='delivered').count()

    # Product sales breakdown
    product_sales = orders_in_period.values('product__name').annotate(
        total_quantity=Sum('quantity'),
        total_revenue=Sum('total_amount'),
        order_count=Count('id')
    ).order_by('-total_revenue')

    # Inventory Report
    current_inventory = Inventory.objects.select_related('product').all()
    low_stock_count = current_inventory.filter(current_stock__lte=F('minimum_stock')).count()

    # Delivery Report
    deliveries_in_period = DeliveryLog.objects.filter(
        delivery_date__range=[start_date, end_date]
    )

    total_deliveries = deliveries_in_period.count()
    total_delivery_cost = deliveries_in_period.aggregate(total=Sum('total_cost'))['total'] or 0

    context = {
        'start_date': start_date.date(),
        'end_date': end_date.date(),
        'total_sales': total_sales,
        'total_orders': total_orders,
        'delivered_orders': delivered_orders,
        'product_sales': product_sales,
        'current_inventory': current_inventory,
        'low_stock_count': low_stock_count,
        'total_deliveries': total_deliveries,
        'total_delivery_cost': total_delivery_cost,
    }

    if request.htmx:
        return render(request, 'partials/reports_content.html', context)
    return render(request, 'core/reports.html', context)
