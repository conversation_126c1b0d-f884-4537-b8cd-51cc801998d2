<div class="bg-white shadow overflow-hidden sm:rounded-md">
    {% if orders %}
        <ul class="divide-y divide-gray-200">
            {% for order in orders %}
            <li>
                <div class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <p class="text-sm font-medium text-blue-600 truncate">
                                Order #{{ order.id }}
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                   {% if order.status == 'delivered' %}bg-green-100 text-green-800
                                   {% elif order.status == 'out_for_delivery' %}bg-yellow-100 text-yellow-800
                                   {% elif order.status == 'confirmed' %}bg-blue-100 text-blue-800
                                   {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                   {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ order.get_status_display }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">{{ order.created_at|date:"M d, Y g:i A" }}</span>
                            <button @click="$dispatch('open-status-modal', { orderId: {{ order.id }} })"
                                    class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200">
                                Update Status
                            </button>
                        </div>
                    </div>
                    
                    <div class="mt-2 sm:flex sm:justify-between">
                        <div class="sm:flex sm:flex-wrap sm:space-x-6">
                            <p class="flex items-center text-sm text-gray-500">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                {{ order.customer.user.get_full_name|default:order.customer.user.username }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13z" clip-rule="evenodd"></path>
                                </svg>
                                {{ order.product.name }} x{{ order.quantity }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                </svg>
                                ₱{{ order.total_amount }}
                            </p>
                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                {{ order.get_delivery_type_display }}
                            </p>
                        </div>
                    </div>
                    
                    {% if order.delivery_type == 'delivery' and order.delivery_address %}
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">
                            <span class="font-medium">Delivery Address:</span> {{ order.delivery_address }}
                        </p>
                    </div>
                    {% endif %}
                    
                    {% if order.notes %}
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">
                            <span class="font-medium">Notes:</span> {{ order.notes }}
                        </p>
                    </div>
                    {% endif %}
                    
                    <!-- Contact Information -->
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">
                            <span class="font-medium">Contact:</span> 
                            {% if order.customer.phone_number %}
                                {{ order.customer.phone_number }}
                            {% else %}
                                {{ order.customer.user.email|default:"No contact info" }}
                            {% endif %}
                        </p>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
        
        <!-- Pagination could be added here if needed -->
        
    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
            <p class="mt-1 text-sm text-gray-500">
                {% if status_filter or search_query %}
                    No orders match your current filters.
                {% else %}
                    No orders have been placed yet.
                {% endif %}
            </p>
        </div>
    {% endif %}
</div>
