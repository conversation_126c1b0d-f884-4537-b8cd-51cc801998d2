<!DOCTYPE html>
<html lang="en" x-data="{ mobileMenuOpen: false }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Prycegas - LPG Dealer System{% endblock %}</title>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3/unpoly.min.css">

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}"
    
    <!-- Custom styles -->
    <style>
        [x-cloak] { display: none !important; }

        /* Custom scrollbar for better mobile experience */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Loading spinner */
        .htmx-indicator {
            opacity: 0;
            transition: opacity 200ms ease-in;
        }

        .htmx-request .htmx-indicator {
            opacity: 1;
        }

        .htmx-request.htmx-indicator {
            opacity: 1;
        }

        /* Smooth transitions */
        .transition-all {
            transition: all 0.3s ease;
        }

        /* Mobile-first responsive utilities */
        @media (max-width: 767px) {
            .mobile-full-width {
                width: 100vw;
                margin-left: calc(-50vw + 50%);
            }

            /* Touch-friendly buttons on mobile */
            .btn-touch {
                min-height: 44px;
                min-width: 44px;
            }

            /* Larger tap targets */
            .tap-target {
                padding: 12px;
            }
        }

        /* Tablet optimizations */
        @media (min-width: 768px) and (max-width: 1023px) {
            .tablet-padding {
                padding: 1.5rem;
            }
        }

        /* Desktop optimizations */
        @media (min-width: 1024px) {
            .desktop-hover:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }
        }

        /* Large desktop optimizations */
        @media (min-width: 1440px) {
            .large-desktop-container {
                max-width: 1400px;
            }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-blue-600 shadow-lg">
        <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8">
            <div class="flex justify-between h-14 sm:h-16">
                <div class="flex items-center">
                    <a href="{% url 'core:dashboard' %}" class="flex-shrink-0 flex items-center">
                        <h1 class="text-white text-lg sm:text-xl lg:text-2xl font-bold">Prycegas</h1>
                    </a>

                    <!-- Desktop Navigation -->
                    <div class="hidden lg:ml-6 lg:flex lg:space-x-4 xl:space-x-8">
                        {% if user.is_authenticated %}
                            {% if user.is_staff %}
                                <a href="{% url 'core:admin_dashboard' %}" class="text-blue-100 hover:text-white px-2 xl:px-3 py-2 rounded-md text-sm xl:text-base font-medium transition-colors duration-200">Dashboard</a>
                                <a href="{% url 'core:orders' %}" class="text-blue-100 hover:text-white px-2 xl:px-3 py-2 rounded-md text-sm xl:text-base font-medium transition-colors duration-200">Orders</a>
                                <a href="{% url 'core:inventory' %}" class="text-blue-100 hover:text-white px-2 xl:px-3 py-2 rounded-md text-sm xl:text-base font-medium transition-colors duration-200">Inventory</a>
                                <a href="{% url 'core:reports' %}" class="text-blue-100 hover:text-white px-2 xl:px-3 py-2 rounded-md text-sm xl:text-base font-medium transition-colors duration-200">Reports</a>
                            {% else %}
                                <a href="{% url 'core:customer_dashboard' %}" class="text-blue-100 hover:text-white px-2 xl:px-3 py-2 rounded-md text-sm xl:text-base font-medium transition-colors duration-200">Dashboard</a>
                                <a href="{% url 'core:place_order' %}" class="text-blue-100 hover:text-white px-2 xl:px-3 py-2 rounded-md text-sm xl:text-base font-medium transition-colors duration-200">Place Order</a>
                                <a href="{% url 'core:my_orders' %}" class="text-blue-100 hover:text-white px-2 xl:px-3 py-2 rounded-md text-sm xl:text-base font-medium transition-colors duration-200">My Orders</a>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
                
                <!-- User menu -->
                <div class="flex items-center space-x-2 sm:space-x-4">
                    {% if user.is_authenticated %}
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-xs sm:text-sm text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 rounded-md px-2 py-1">
                                <span class="mr-1 sm:mr-2 truncate max-w-24 sm:max-w-32 lg:max-w-none">{{ user.get_full_name|default:user.username }}</span>
                                <svg class="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>

                            <div x-show="open" @click.away="open = false" x-cloak class="absolute right-0 mt-2 w-40 sm:w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="{% url 'auth:profile' %}" class="block px-3 sm:px-4 py-2 text-xs sm:text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">Profile</a>
                                <form method="post" action="{% url 'auth:logout' %}" class="block">
                                    {% csrf_token %}
                                    <button type="submit" class="w-full text-left px-3 sm:px-4 py-2 text-xs sm:text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">Logout</button>
                                </form>
                            </div>
                        </div>
                    {% else %}
                        <div class="hidden sm:flex space-x-2 lg:space-x-4">
                            <a href="{% url 'auth:login' %}" class="text-blue-100 hover:text-white px-2 lg:px-3 py-2 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-colors duration-200">Login</a>
                            <a href="{% url 'auth:register' %}" class="bg-blue-500 hover:bg-blue-400 text-white px-3 lg:px-4 py-2 rounded-md text-xs sm:text-sm lg:text-base font-medium transition-colors duration-200">Register</a>
                        </div>
                        <!-- Mobile auth buttons -->
                        <div class="sm:hidden flex space-x-1">
                            <a href="{% url 'auth:login' %}" class="text-blue-100 hover:text-white px-2 py-1 rounded text-xs font-medium">Login</a>
                            <a href="{% url 'auth:register' %}" class="bg-blue-500 hover:bg-blue-400 text-white px-2 py-1 rounded text-xs font-medium">Register</a>
                        </div>
                    {% endif %}

                    <!-- Mobile menu button -->
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="lg:hidden ml-2 sm:ml-4 text-blue-100 hover:text-white focus:outline-none focus:ring-2 focus:ring-white rounded-md p-1">
                        <svg class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             x-cloak class="lg:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-blue-700 border-t border-blue-500">
                {% if user.is_authenticated %}
                    {% if user.is_staff %}
                        <a href="{% url 'core:admin_dashboard' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Dashboard</a>
                        <a href="{% url 'core:orders' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Orders</a>
                        <a href="{% url 'core:inventory' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Inventory</a>
                        <a href="{% url 'core:reports' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Reports</a>
                    {% else %}
                        <a href="{% url 'core:customer_dashboard' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Dashboard</a>
                        <a href="{% url 'core:place_order' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Place Order</a>
                        <a href="{% url 'core:my_orders' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">My Orders</a>
                    {% endif %}
                {% else %}
                    <a href="{% url 'auth:login' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Login</a>
                    <a href="{% url 'auth:register' %}" class="text-blue-100 hover:text-white hover:bg-blue-600 block px-3 py-2 rounded-md text-sm sm:text-base font-medium transition-colors duration-200">Register</a>
                {% endif %}
            </div>
        </div>
    </nav>
    
    <!-- Messages -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 mt-2 sm:mt-4">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} mb-2 sm:mb-4 p-3 sm:p-4 rounded-md {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}"
                     x-data="{ show: true }"
                     x-show="show"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform translate-y-2"
                     x-init="setTimeout(() => show = false, 5000)">
                    <div class="flex justify-between items-start">
                        <span class="text-sm sm:text-base pr-2">{{ message }}</span>
                        <button @click="show = false" class="ml-2 text-lg sm:text-xl font-medium hover:opacity-70 transition-opacity duration-200 flex-shrink-0">×</button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Alpine.js Notification System -->
    <div x-data="{ notifications: $store.app.notifications }"
         class="fixed top-4 right-4 z-50 space-y-2"
         x-show="notifications.length > 0">
        <template x-for="notification in notifications" :key="notification.id">
            <div x-show="true"
                 x-transition:enter="transform ease-out duration-300 transition"
                 x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
                 x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
                 x-transition:leave="transition ease-in duration-100"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="max-w-sm w-full shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
                 :class="{
                     'bg-green-50 border-green-400': notification.type === 'success',
                     'bg-red-50 border-red-400': notification.type === 'error',
                     'bg-yellow-50 border-yellow-400': notification.type === 'warning',
                     'bg-blue-50 border-blue-400': notification.type === 'info'
                 }">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <!-- Success Icon -->
                            <svg x-show="notification.type === 'success'" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <!-- Error Icon -->
                            <svg x-show="notification.type === 'error'" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <!-- Warning Icon -->
                            <svg x-show="notification.type === 'warning'" class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <!-- Info Icon -->
                            <svg x-show="notification.type === 'info'" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium"
                               :class="{
                                   'text-green-900': notification.type === 'success',
                                   'text-red-900': notification.type === 'error',
                                   'text-yellow-900': notification.type === 'warning',
                                   'text-blue-900': notification.type === 'info'
                               }"
                               x-text="notification.message"></p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="$store.app.removeNotification(notification.id)"
                                    class="rounded-md inline-flex focus:outline-none focus:ring-2 focus:ring-offset-2"
                                    :class="{
                                        'text-green-500 hover:text-green-600 focus:ring-green-500': notification.type === 'success',
                                        'text-red-500 hover:text-red-600 focus:ring-red-500': notification.type === 'error',
                                        'text-yellow-500 hover:text-yellow-600 focus:ring-yellow-500': notification.type === 'warning',
                                        'text-blue-500 hover:text-blue-600 focus:ring-blue-500': notification.type === 'info'
                                    }">
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
    
    <!-- Main content -->
    <main class="max-w-7xl mx-auto py-4 sm:py-6 lg:py-8 px-2 sm:px-4 lg:px-6 xl:px-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-8 sm:mt-12">
        <div class="max-w-7xl mx-auto py-6 sm:py-8 px-2 sm:px-4 lg:px-6 xl:px-8">
            <div class="text-center">
                <p class="text-sm sm:text-base">&copy; 2024 Vios Prycegas Tambulig Station. All rights reserved.</p>
                <p class="mt-1 sm:mt-2 text-xs sm:text-sm text-gray-400">Rural LPG Dealer Management System</p>
            </div>
        </div>
    </footer>
    
    <!-- Global Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed top-4 right-4 z-50">
        <div class="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        </div>
    </div>

    <!-- Global Alpine.js Data -->
    <script>
        // Global Alpine.js store for app state
        document.addEventListener('alpine:init', () => {
            Alpine.store('app', {
                loading: false,
                notifications: [],

                addNotification(message, type = 'info') {
                    const id = Date.now();
                    this.notifications.push({ id, message, type });

                    // Auto-remove after 5 seconds
                    setTimeout(() => {
                        this.removeNotification(id);
                    }, 5000);
                },

                removeNotification(id) {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }
            });
        });

        // HTMX configuration
        document.addEventListener('DOMContentLoaded', function() {
            // Add CSRF token to all HTMX requests
            document.body.addEventListener('htmx:configRequest', function(evt) {
                evt.detail.headers['X-CSRFToken'] = document.querySelector('[name=csrfmiddlewaretoken]').value;
            });

            // Show loading indicator for HTMX requests
            document.body.addEventListener('htmx:beforeRequest', function(evt) {
                Alpine.store('app').loading = true;
            });

            document.body.addEventListener('htmx:afterRequest', function(evt) {
                Alpine.store('app').loading = false;
            });

            // Handle HTMX errors
            document.body.addEventListener('htmx:responseError', function(evt) {
                Alpine.store('app').addNotification('An error occurred. Please try again.', 'error');
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
