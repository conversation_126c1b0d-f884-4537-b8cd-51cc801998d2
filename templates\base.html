<!DOCTYPE html>
<html lang="en" x-data="{ mobileMenuOpen: false, sidebarOpen: true, sidebarCollapsed: false }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Prycegas - LPG Dealer System{% endblock %}</title>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3/unpoly.min.css">

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}"
    
    <!-- Custom styles -->
    <style>
        [x-cloak] { display: none !important; }

        /* Custom scrollbar for better mobile experience */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Loading spinner */
        .htmx-indicator {
            opacity: 0;
            transition: opacity 200ms ease-in;
        }

        .htmx-request .htmx-indicator {
            opacity: 1;
        }

        .htmx-request.htmx-indicator {
            opacity: 1;
        }

        /* Smooth transitions */
        .transition-all {
            transition: all 0.3s ease;
        }

        /* Mobile-first responsive utilities */
        @media (max-width: 767px) {
            .mobile-full-width {
                width: 100vw;
                margin-left: calc(-50vw + 50%);
            }

            /* Touch-friendly buttons on mobile */
            .btn-touch {
                min-height: 44px;
                min-width: 44px;
            }

            /* Larger tap targets */
            .tap-target {
                padding: 12px;
            }
        }

        /* Tablet optimizations */
        @media (min-width: 768px) and (max-width: 1023px) {
            .tablet-padding {
                padding: 1.5rem;
            }
        }

        /* Desktop optimizations */
        @media (min-width: 1024px) {
            .desktop-hover:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }
        }

        /* Large desktop optimizations */
        @media (min-width: 1440px) {
            .large-desktop-container {
                max-width: 1400px;
            }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="min-h-screen" style="background-color: var(--background-dark);">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 sidebar transform transition-transform duration-300 ease-in-out md:translate-x-0"
         :class="{ 'sidebar-collapsed': sidebarCollapsed, '-translate-x-full': !sidebarOpen }"
         x-show="sidebarOpen || window.innerWidth >= 768"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="-translate-x-full"
         x-transition:enter-end="translate-x-0"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="translate-x-0"
         x-transition:leave-end="-translate-x-full">

        <!-- Sidebar Header -->
        <div class="flex items-center justify-between h-16 px-4 border-b" style="border-color: var(--border-dark);">
            <a href="{% url 'core:dashboard' %}" class="flex items-center">
                <h1 class="text-xl font-bold" style="color: var(--primary-orange);" x-show="!sidebarCollapsed">Prycegas</h1>
                <h1 class="text-xl font-bold" style="color: var(--primary-orange);" x-show="sidebarCollapsed" x-cloak>P</h1>
            </a>
            <button @click="sidebarCollapsed = !sidebarCollapsed"
                    class="sidebar-toggle md:block hidden">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M11 19l-7-7 7-7m8 14l-7-7 7-7" x-show="!sidebarCollapsed"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M13 5l7 7-7 7M5 5l7 7-7 7" x-show="sidebarCollapsed" x-cloak></path>
                </svg>
            </button>
        </div>

        <!-- Sidebar Navigation -->
        <nav class="mt-8 px-4">
            {% if user.is_authenticated %}
                {% if user.is_staff %}
                    <!-- Admin Navigation -->
                    <div class="space-y-2">
                        <a href="{% url 'core:admin_dashboard' %}" class="sidebar-nav-item">
                            <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                            </svg>
                            <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Dashboard</span>
                        </a>
                        <a href="{% url 'core:orders' %}" class="sidebar-nav-item">
                            <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Orders</span>
                        </a>
                        <a href="{% url 'core:inventory' %}" class="sidebar-nav-item">
                            <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Inventory</span>
                        </a>
                        <a href="{% url 'core:reports' %}" class="sidebar-nav-item">
                            <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Reports</span>
                        </a>
                    </div>
                {% else %}
                    <!-- Customer Navigation -->
                    <div class="space-y-2">
                        <a href="{% url 'core:customer_dashboard' %}" class="sidebar-nav-item">
                            <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                            </svg>
                            <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Dashboard</span>
                        </a>
                        <a href="{% url 'core:place_order' %}" class="sidebar-nav-item">
                            <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Place Order</span>
                        </a>
                        <a href="{% url 'core:my_orders' %}" class="sidebar-nav-item">
                            <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span class="sidebar-nav-text" x-show="!sidebarCollapsed">My Orders</span>
                        </a>
                    </div>
                {% endif %}
            {% endif %}
        </nav>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-show="sidebarOpen && window.innerWidth < 768"
         @click="sidebarOpen = false"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="sidebar-overlay md:hidden"
         x-cloak></div>

    <!-- Top Header Bar -->
    <div class="fixed top-0 right-0 left-0 md:left-64 z-40 h-16 flex items-center justify-between px-4 border-b transition-all duration-300"
         :class="{ 'md:left-16': sidebarCollapsed }"
         style="background-color: var(--background-dark-secondary); border-color: var(--border-dark);">

        <!-- Mobile menu button -->
        <button @click="sidebarOpen = !sidebarOpen" class="md:hidden p-2 rounded-lg" style="color: var(--text-light);">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>

        <!-- Mobile title (shown when sidebar is closed) -->
        <h1 class="md:hidden text-xl font-bold" style="color: var(--primary-orange);" x-show="!sidebarOpen">Prycegas</h1>

        <!-- User menu -->
        <div class="flex items-center space-x-4">
            {% if user.is_authenticated %}
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-md px-3 py-2 transition-colors duration-200"
                            style="color: var(--text-light); focus:ring-color: var(--primary-orange); focus:ring-offset-color: var(--background-dark-secondary);">
                        <span class="mr-2 truncate max-w-32">{{ user.get_full_name|default:user.username }}</span>
                        <svg class="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>

                    <div x-show="open" @click.away="open = false" x-cloak
                         class="absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 z-50"
                         style="background-color: var(--background-dark-secondary); border: 1px solid var(--border-dark);">
                        <a href="{% url 'auth:profile' %}" class="block px-4 py-2 text-sm transition-colors duration-200"
                           style="color: var(--text-light);"
                           onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
                           onmouseout="this.style.backgroundColor='transparent'">Profile</a>
                        <form method="post" action="{% url 'auth:logout' %}" class="block">
                            {% csrf_token %}
                            <button type="submit" class="w-full text-left px-4 py-2 text-sm transition-colors duration-200"
                                    style="color: var(--text-light);"
                                    onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
                                    onmouseout="this.style.backgroundColor='transparent'">Logout</button>
                        </form>
                    </div>
                </div>
            {% else %}
                <div class="flex space-x-2">
                    <a href="{% url 'auth:login' %}" class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                       style="color: var(--text-gray);"
                       onmouseover="this.style.color='var(--text-light)'"
                       onmouseout="this.style.color='var(--text-gray)'">Login</a>
                    <a href="{% url 'auth:register' %}" class="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                       style="background-color: var(--primary-orange); color: white;"
                       onmouseover="this.style.backgroundColor='var(--primary-orange-hover)'"
                       onmouseout="this.style.backgroundColor='var(--primary-orange)'">Register</a>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Messages -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 mt-2 sm:mt-4">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} mb-2 sm:mb-4 p-3 sm:p-4 rounded-md {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}"
                     x-data="{ show: true }"
                     x-show="show"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform translate-y-2"
                     x-init="setTimeout(() => show = false, 5000)">
                    <div class="flex justify-between items-start">
                        <span class="text-sm sm:text-base pr-2">{{ message }}</span>
                        <button @click="show = false" class="ml-2 text-lg sm:text-xl font-medium hover:opacity-70 transition-opacity duration-200 flex-shrink-0">×</button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Alpine.js Notification System -->
    <div x-data="{ notifications: $store.app.notifications }"
         class="fixed top-4 right-4 z-50 space-y-2"
         x-show="notifications.length > 0">
        <template x-for="notification in notifications" :key="notification.id">
            <div x-show="true"
                 x-transition:enter="transform ease-out duration-300 transition"
                 x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
                 x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
                 x-transition:leave="transition ease-in duration-100"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="max-w-sm w-full shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
                 :class="{
                     'bg-green-50 border-green-400': notification.type === 'success',
                     'bg-red-50 border-red-400': notification.type === 'error',
                     'bg-yellow-50 border-yellow-400': notification.type === 'warning',
                     'bg-blue-50 border-blue-400': notification.type === 'info'
                 }">
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <!-- Success Icon -->
                            <svg x-show="notification.type === 'success'" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <!-- Error Icon -->
                            <svg x-show="notification.type === 'error'" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <!-- Warning Icon -->
                            <svg x-show="notification.type === 'warning'" class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <!-- Info Icon -->
                            <svg x-show="notification.type === 'info'" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1 pt-0.5">
                            <p class="text-sm font-medium"
                               :class="{
                                   'text-green-900': notification.type === 'success',
                                   'text-red-900': notification.type === 'error',
                                   'text-yellow-900': notification.type === 'warning',
                                   'text-blue-900': notification.type === 'info'
                               }"
                               x-text="notification.message"></p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button @click="$store.app.removeNotification(notification.id)"
                                    class="rounded-md inline-flex focus:outline-none focus:ring-2 focus:ring-offset-2"
                                    :class="{
                                        'text-green-500 hover:text-green-600 focus:ring-green-500': notification.type === 'success',
                                        'text-red-500 hover:text-red-600 focus:ring-red-500': notification.type === 'error',
                                        'text-yellow-500 hover:text-yellow-600 focus:ring-yellow-500': notification.type === 'warning',
                                        'text-blue-500 hover:text-blue-600 focus:ring-blue-500': notification.type === 'info'
                                    }">
                                <span class="sr-only">Close</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
    
    <!-- Main content -->
    <main class="pt-16 transition-all duration-300 md:ml-64"
          :class="{ 'md:ml-16': sidebarCollapsed }"
          style="background-color: var(--background-dark); min-height: 100vh;">

        <!-- Page content -->
        <div class="px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="mt-8 sm:mt-12 transition-all duration-300 md:ml-64"
            :class="{ 'md:ml-16': sidebarCollapsed }"
            style="background-color: var(--background-dark-secondary); border-top: 1px solid var(--border-dark);">
        <div class="py-6 sm:py-8 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-sm sm:text-base" style="color: var(--text-light);">&copy; 2024 Vios Prycegas Tambulig Station. All rights reserved.</p>
                <p class="mt-1 sm:mt-2 text-xs sm:text-sm" style="color: var(--text-gray);">Rural LPG Dealer Management System</p>
            </div>
        </div>
    </footer>
    
    <!-- Global Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed top-20 right-4 z-50">
        <div class="px-4 py-2 rounded-lg shadow-lg flex items-center"
             style="background-color: var(--primary-orange); color: white;">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        </div>
    </div>

    <!-- Global Alpine.js Data -->
    <script>
        // Global Alpine.js store for app state
        document.addEventListener('alpine:init', () => {
            Alpine.store('app', {
                loading: false,
                notifications: [],

                addNotification(message, type = 'info') {
                    const id = Date.now();
                    this.notifications.push({ id, message, type });

                    // Auto-remove after 5 seconds
                    setTimeout(() => {
                        this.removeNotification(id);
                    }, 5000);
                },

                removeNotification(id) {
                    this.notifications = this.notifications.filter(n => n.id !== id);
                }
            });
        });

        // HTMX configuration
        document.addEventListener('DOMContentLoaded', function() {
            // Add CSRF token to all HTMX requests
            document.body.addEventListener('htmx:configRequest', function(evt) {
                evt.detail.headers['X-CSRFToken'] = document.querySelector('[name=csrfmiddlewaretoken]').value;
            });

            // Show loading indicator for HTMX requests
            document.body.addEventListener('htmx:beforeRequest', function(evt) {
                Alpine.store('app').loading = true;
            });

            document.body.addEventListener('htmx:afterRequest', function(evt) {
                Alpine.store('app').loading = false;
            });

            // Handle HTMX errors
            document.body.addEventListener('htmx:responseError', function(evt) {
                Alpine.store('app').addNotification('An error occurred. Please try again.', 'error');
            });

            // Add active class to current navigation item
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.sidebar-nav-item');

            navItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });

            // Handle responsive sidebar behavior
            function handleResize() {
                const sidebar = document.querySelector('[x-data]');
                if (window.innerWidth < 768) {
                    // Mobile: close sidebar by default
                    if (sidebar && sidebar.__x) {
                        sidebar.__x.$data.sidebarOpen = false;
                    }
                } else {
                    // Desktop: open sidebar by default
                    if (sidebar && sidebar.__x) {
                        sidebar.__x.$data.sidebarOpen = true;
                    }
                }
            }

            // Initial check
            handleResize();

            // Listen for window resize
            window.addEventListener('resize', handleResize);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
