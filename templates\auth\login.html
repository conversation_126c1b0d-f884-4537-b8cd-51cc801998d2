{% extends 'base/landing.html' %}

{% block title %}Login - Prycegas{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-8 sm:py-12 px-2 sm:px-4 lg:px-8">
    <div class="max-w-sm sm:max-w-md w-full space-y-6 sm:space-y-8">
        <div>
            <h2 class="mt-4 sm:mt-6 text-center text-2xl sm:text-3xl font-extrabold text-gray-900">
                Sign in to your account
            </h2>
            <p class="mt-2 text-center text-xs sm:text-sm text-gray-600">
                Or
                <a href="{% url 'auth:register' %}" class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
                    create a new account
                </a>
            </p>
        </div>
        
        <form class="mt-6 sm:mt-8 space-y-4 sm:space-y-6" method="post" hx-post="{% url 'auth:login' %}" hx-target="#login-form" hx-swap="outerHTML">
            <div id="login-form">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="bg-red-100 border border-red-400 text-red-700 px-3 sm:px-4 py-3 rounded mb-4 text-xs sm:text-sm">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}

                <div class="rounded-md shadow-sm space-y-3 sm:-space-y-px">
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="sr-only">Username</label>
                        <input id="{{ form.username.id_for_label }}"
                               name="{{ form.username.name }}"
                               type="text"
                               required
                               class="appearance-none relative block w-full px-3 py-3 sm:py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md sm:rounded-t-md sm:rounded-b-none focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 text-sm sm:text-base"
                               placeholder="Username"
                               value="{{ form.username.value|default:'' }}">
                        {% if form.username.errors %}
                            <div class="text-red-600 text-xs sm:text-sm mt-1">{{ form.username.errors }}</div>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.password.id_for_label }}" class="sr-only">Password</label>
                        <input id="{{ form.password.id_for_label }}"
                               name="{{ form.password.name }}"
                               type="password"
                               required
                               class="appearance-none relative block w-full px-3 py-3 sm:py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md sm:rounded-b-md sm:rounded-t-none focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 text-sm sm:text-base"
                               placeholder="Password">
                        {% if form.password.errors %}
                            <div class="text-red-600 text-xs sm:text-sm mt-1">{{ form.password.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                            Remember me
                        </label>
                    </div>
                </div>

                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                        </span>
                        Sign in
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}
