<!-- Dashboard Header Component -->
<div class="fixed top-0 right-0 left-0 md:left-64 z-40 h-16 flex items-center justify-between px-4 border-b transition-all duration-300"
     :class="{ 'md:left-16': sidebarCollapsed }"
     style="background-color: var(--background-dark-secondary); border-color: var(--border-dark);">
    
    <!-- Mobile menu button -->
    <button @click="sidebarOpen = !sidebarOpen" class="md:hidden p-2 rounded-lg" style="color: var(--text-light);">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>
    
    <!-- Mobile title (shown when sidebar is closed) -->
    <h1 class="md:hidden text-xl font-bold" style="color: var(--primary-orange);" x-show="!sidebarOpen">Prycegas</h1>
    
    <!-- User menu -->
    <div class="flex items-center space-x-4">
        {% if user.is_authenticated %}
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 rounded-md px-3 py-2 transition-colors duration-200"
                        style="color: var(--text-light); focus:ring-color: var(--primary-orange); focus:ring-offset-color: var(--background-dark-secondary);">
                    <span class="mr-2 truncate max-w-32">{{ user.get_full_name|default:user.username }}</span>
                    <svg class="h-5 w-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>

                <div x-show="open" @click.away="open = false" x-cloak 
                     class="absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 z-50"
                     style="background-color: var(--background-dark-secondary); border: 1px solid var(--border-dark);">
                    <a href="{% url 'auth:profile' %}" class="block px-4 py-2 text-sm transition-colors duration-200"
                       style="color: var(--text-light);"
                       onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
                       onmouseout="this.style.backgroundColor='transparent'">Profile</a>
                    <form method="post" action="{% url 'auth:logout' %}" class="block">
                        {% csrf_token %}
                        <button type="submit" class="w-full text-left px-4 py-2 text-sm transition-colors duration-200"
                                style="color: var(--text-light);"
                                onmouseover="this.style.backgroundColor='var(--background-dark-tertiary)'"
                                onmouseout="this.style.backgroundColor='transparent'">Logout</button>
                    </form>
                </div>
            </div>
        {% else %}
            <div class="flex space-x-2">
                <a href="{% url 'auth:login' %}" class="px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                   style="color: var(--text-gray);"
                   onmouseover="this.style.color='var(--text-light)'"
                   onmouseout="this.style.color='var(--text-gray)'">Login</a>
                <a href="{% url 'auth:register' %}" class="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                   style="background-color: var(--primary-orange); color: white;"
                   onmouseover="this.style.backgroundColor='var(--primary-orange-hover)'"
                   onmouseout="this.style.backgroundColor='var(--primary-orange)'">Register</a>
            </div>
        {% endif %}
    </div>
</div>
