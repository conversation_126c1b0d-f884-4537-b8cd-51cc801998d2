<div class="px-4 py-5 sm:p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Order #{{ order.id }}</h3>
        <button @click="$dispatch('close-modal', 'order-detail')" class="text-gray-400 hover:text-gray-600">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    
    <dl class="space-y-4">
        <div>
            <dt class="text-sm font-medium text-gray-500">Status</dt>
            <dd class="mt-1">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                       {% if order.status == 'delivered' %}bg-green-100 text-green-800
                       {% elif order.status == 'out_for_delivery' %}bg-yellow-100 text-yellow-800
                       {% elif order.status == 'confirmed' %}bg-blue-100 text-blue-800
                       {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                       {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ order.get_status_display }}
                </span>
            </dd>
        </div>
        
        <div>
            <dt class="text-sm font-medium text-gray-500">Product</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ order.product.name }}</dd>
        </div>
        
        <div>
            <dt class="text-sm font-medium text-gray-500">Quantity</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ order.quantity }}</dd>
        </div>
        
        <div>
            <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
            <dd class="mt-1 text-sm text-gray-900 font-semibold">₱{{ order.total_amount }}</dd>
        </div>
        
        <div>
            <dt class="text-sm font-medium text-gray-500">Delivery Type</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ order.get_delivery_type_display }}</dd>
        </div>
        
        {% if order.delivery_type == 'delivery' and order.delivery_address %}
        <div>
            <dt class="text-sm font-medium text-gray-500">Delivery Address</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ order.delivery_address }}</dd>
        </div>
        {% endif %}
        
        <div>
            <dt class="text-sm font-medium text-gray-500">Order Date</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ order.created_at|date:"F d, Y g:i A" }}</dd>
        </div>
        
        {% if order.delivered_at %}
        <div>
            <dt class="text-sm font-medium text-gray-500">Delivered Date</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ order.delivered_at|date:"F d, Y g:i A" }}</dd>
        </div>
        {% endif %}
        
        {% if order.notes %}
        <div>
            <dt class="text-sm font-medium text-gray-500">Notes</dt>
            <dd class="mt-1 text-sm text-gray-900">{{ order.notes }}</dd>
        </div>
        {% endif %}
    </dl>
</div>

<div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
    <button @click="$dispatch('close-modal', 'order-detail')" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
        Close
    </button>
</div>
