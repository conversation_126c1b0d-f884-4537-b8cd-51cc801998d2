from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Customer, LPGProduct, Inventory, Order, DeliveryLog
from decimal import Decimal


class Command(BaseCommand):
    help = 'Populate database with sample data for Prycegas system'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create LPG Products
        products_data = [
            {'name': '11kg LPG Tank', 'weight_kg': 11.0, 'price': 850.00, 'description': 'Standard 11kg LPG tank for household use'},
            {'name': '22kg LPG Tank', 'weight_kg': 22.0, 'price': 1650.00, 'description': 'Large 22kg LPG tank for commercial use'},
            {'name': '50kg LPG Tank', 'weight_kg': 50.0, 'price': 3500.00, 'description': 'Industrial 50kg LPG tank'},
        ]
        
        for product_data in products_data:
            product, created = LPGProduct.objects.get_or_create(
                name=product_data['name'],
                defaults=product_data
            )
            if created:
                self.stdout.write(f'Created product: {product.name}')
                
                # Create inventory for each product
                Inventory.objects.get_or_create(
                    product=product,
                    defaults={'current_stock': 50, 'minimum_stock': 10}
                )
                self.stdout.write(f'Created inventory for: {product.name}')
        
        # Create sample customers
        customers_data = [
            {'username': 'juan_dela_cruz', 'email': '<EMAIL>', 'first_name': 'Juan', 'last_name': 'Dela Cruz', 'phone': '09123456789', 'address': 'Barangay Centro, Tambulig, Zamboanga del Sur'},
            {'username': 'maria_santos', 'email': '<EMAIL>', 'first_name': 'Maria', 'last_name': 'Santos', 'phone': '09987654321', 'address': 'Barangay Poblacion, Tambulig, Zamboanga del Sur'},
            {'username': 'pedro_garcia', 'email': '<EMAIL>', 'first_name': 'Pedro', 'last_name': 'Garcia', 'phone': '09555123456', 'address': 'Barangay San Jose, Tambulig, Zamboanga del Sur'},
        ]
        
        for customer_data in customers_data:
            user, created = User.objects.get_or_create(
                username=customer_data['username'],
                defaults={
                    'email': customer_data['email'],
                    'first_name': customer_data['first_name'],
                    'last_name': customer_data['last_name'],
                }
            )
            if created:
                user.set_password('password123')
                user.save()
                self.stdout.write(f'Created user: {user.username}')
                
                Customer.objects.get_or_create(
                    user=user,
                    defaults={
                        'phone_number': customer_data['phone'],
                        'address': customer_data['address']
                    }
                )
                self.stdout.write(f'Created customer profile for: {user.username}')
        
        # Create sample orders
        if Customer.objects.exists() and LPGProduct.objects.exists():
            customer = Customer.objects.first()
            product = LPGProduct.objects.first()
            
            Order.objects.get_or_create(
                customer=customer,
                product=product,
                quantity=2,
                defaults={
                    'delivery_type': 'delivery',
                    'delivery_address': customer.address,
                    'status': 'pending',
                    'total_amount': product.price * 2
                }
            )
            self.stdout.write('Created sample order')
        
        self.stdout.write(self.style.SUCCESS('Successfully populated database with sample data!'))
