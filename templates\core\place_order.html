{% extends 'base/dashboard.html' %}

{% block title %}Place Order - Prycegas{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-2 sm:px-4 lg:px-6">
    <div class="bg-white shadow-sm sm:shadow-lg rounded-lg overflow-hidden">
        <div class="px-4 sm:px-6 py-4 sm:py-6 bg-blue-600">
            <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-white">Place New Order</h1>
            <p class="text-sm sm:text-base text-blue-100 mt-1">Order your LPG tanks with delivery or pickup options</p>
        </div>
        
        <div class="p-4 sm:p-6 lg:p-8" x-data="orderForm()">
            <form method="post" hx-post="{% url 'core:place_order' %}" hx-target="#order-form" hx-swap="outerHTML">
                <div id="order-form">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-3 sm:px-4 py-3 rounded mb-4 text-sm sm:text-base">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <div class="space-y-4 sm:space-y-6">
                        <!-- Product Selection -->
                        <div>
                            <label for="{{ form.product.id_for_label }}" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                                LPG Product
                            </label>
                            {{ form.product }}
                            {% if form.product.errors %}
                                <div class="text-red-600 text-xs sm:text-sm mt-1">{{ form.product.errors }}</div>
                            {% endif %}
                        </div>

                        <!-- Quantity -->
                        <div>
                            <label for="{{ form.quantity.id_for_label }}" class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                                Quantity
                            </label>
                            {{ form.quantity }}
                            {% if form.quantity.errors %}
                                <div class="text-red-600 text-xs sm:text-sm mt-1">{{ form.quantity.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Delivery Type -->
                        <div>
                            <label for="{{ form.delivery_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Delivery Type
                            </label>
                            {{ form.delivery_type }}
                            {% if form.delivery_type.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.delivery_type.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Delivery Address (conditional) -->
                        <div x-show="deliveryType === 'delivery'" x-cloak>
                            <label for="{{ form.delivery_address.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Delivery Address
                            </label>
                            {{ form.delivery_address }}
                            {% if form.delivery_address.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.delivery_address.errors }}</div>
                            {% endif %}
                            <p class="text-sm text-gray-500 mt-1">
                                Please provide a complete address for delivery.
                            </p>
                        </div>
                        
                        <!-- Pickup Information -->
                        <div x-show="deliveryType === 'pickup'" x-cloak>
                            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-blue-800">Pickup Information</h3>
                                        <div class="mt-2 text-sm text-blue-700">
                                            <p>Pickup Location: Vios Prycegas Tambulig Station</p>
                                            <p>Address: Tambulig, Zamboanga del Sur</p>
                                            <p>Hours: Monday - Saturday, 8:00 AM - 6:00 PM</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Notes -->
                        <div>
                            <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Additional Notes (Optional)
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-red-600 text-sm mt-1">{{ form.notes.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- Order Summary -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-3">Order Summary</h3>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Product:</span>
                                    <span class="font-medium" x-text="selectedProduct"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Quantity:</span>
                                    <span class="font-medium" x-text="quantity || '1'"></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Price per unit:</span>
                                    <span class="font-medium" x-text="'₱' + (productPrice || '0.00')"></span>
                                </div>
                                <div class="border-t pt-2">
                                    <div class="flex justify-between text-lg font-bold">
                                        <span>Total:</span>
                                        <span x-text="'₱' + totalAmount"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-8 flex justify-end space-x-3">
                        <a href="{% url 'core:customer_dashboard' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </a>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Place Order
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function orderForm() {
    return {
        deliveryType: 'pickup',
        quantity: 1,
        selectedProduct: '',
        productPrice: 0,
        
        get totalAmount() {
            return (this.productPrice * this.quantity).toFixed(2);
        },
        
        updateDeliveryAddress() {
            // This function is called when delivery type changes
        },
        
        init() {
            // Watch for changes in form fields
            this.$watch('deliveryType', (value) => {
                const addressField = document.querySelector('#id_delivery_address');
                if (addressField) {
                    addressField.required = value === 'delivery';
                }
            });
        }
    }
}
</script>
{% endblock %}
