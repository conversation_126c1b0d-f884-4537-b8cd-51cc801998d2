# Prycegas LPG Dealer System - Deployment Guide

## Overview
This is a web-based LPG dealer and distributor information system built with Django, TailwindCSS, HTMX, Alpine.js, and Unpoly.

## Features
- Customer registration and order management
- Admin/Dealer dashboard with inventory tracking
- Real-time order status updates
- Delivery logging and inventory management
- Business reports and analytics
- Mobile-responsive design
- Progressive enhancement with HTMX/Alpine.js

## Tech Stack
- **Backend**: Django 5.2.4
- **Frontend**: TailwindCSS + HTMX + Alpine.js + Unpoly
- **Database**: SQLite (development) / PostgreSQL (production)
- **Python**: 3.12+

## Local Development Setup

### Prerequisites
- Python 3.12 or higher
- uv (Python package manager)

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Prycegas
   ```

2. **Create virtual environment and install dependencies**
   ```bash
   uv venv
   uv pip install -r requirements.txt
   ```

3. **Run database migrations**
   ```bash
   uv run python manage.py migrate
   ```

4. **Create sample data**
   ```bash
   uv run python manage.py populate_data
   ```

5. **Create superuser (optional)**
   ```bash
   uv run python manage.py createsuperuser
   ```

6. **Collect static files**
   ```bash
   uv run python manage.py collectstatic
   ```

7. **Run development server**
   ```bash
   uv run python manage.py runserver
   ```

8. **Access the application**
   - Open http://127.0.0.1:8000/ in your browser
   - Use sample customer accounts or create new ones
   - Admin interface: http://127.0.0.1:8000/admin/

## Testing

Run the test suite:
```bash
uv run python manage.py test
```

## Production Deployment

### Environment Variables
Create a `.env` file with:
```
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
DATABASE_URL=postgresql://user:password@localhost/dbname
```

### PostgreSQL Setup
1. Install PostgreSQL
2. Create database and user
3. Update DATABASE_URL in .env file
4. Install psycopg2: `uv pip install psycopg2-binary`

### Static Files
For production, configure a web server (nginx/Apache) to serve static files:
```bash
uv run python manage.py collectstatic --noinput
```

### Security Checklist
- [ ] Set DEBUG=False
- [ ] Use strong SECRET_KEY
- [ ] Configure ALLOWED_HOSTS
- [ ] Use HTTPS
- [ ] Set up proper database backups
- [ ] Configure logging
- [ ] Set up monitoring

## User Accounts

### Sample Customer Accounts
- Username: `juan_dela_cruz`, Password: `password123`
- Username: `maria_santos`, Password: `password123`
- Username: `pedro_garcia`, Password: `password123`

### Admin Account
Create using: `uv run python manage.py createsuperuser`

## Key Features

### Customer Features
- User registration and authentication
- Place LPG orders (pickup or delivery)
- View order history and status
- Real-time order tracking
- Mobile-responsive interface

### Admin/Dealer Features
- Dashboard with business metrics
- Order management and status updates
- Inventory tracking and low-stock alerts
- Delivery logging from distributors
- Business reports and analytics
- Customer management

### Technical Features
- Progressive enhancement with HTMX
- Real-time updates without page refresh
- Mobile-first responsive design
- Optimized for rural internet connections
- Accessibility features
- Print-friendly reports

## API Endpoints

The system uses server-rendered templates with HTMX for dynamic updates:
- `/` - Home page
- `/auth/login/` - User login
- `/auth/register/` - Customer registration
- `/customer/` - Customer dashboard
- `/order/` - Place new order
- `/orders/` - Order history
- `/admin-dashboard/` - Admin dashboard
- `/manage/orders/` - Order management
- `/manage/inventory/` - Inventory management
- `/reports/` - Business reports

## Troubleshooting

### Common Issues

1. **Template errors with math filters**
   - Ensure `{% load math_filters %}` is at the top of templates using mathematical operations

2. **Static files not loading**
   - Run `python manage.py collectstatic`
   - Check STATIC_URL and STATICFILES_DIRS settings

3. **Database errors**
   - Run `python manage.py migrate`
   - Check database connection settings

4. **Permission errors**
   - Ensure proper user roles (staff for admin features)
   - Check authentication decorators

## Performance Optimization

- Database indexes on frequently queried fields
- Efficient querysets with select_related/prefetch_related
- Static file compression and caching
- HTMX for partial page updates
- Optimized for mobile and slow connections

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Django and HTMX documentation
3. Check browser console for JavaScript errors
4. Verify database migrations are up to date

## License

This project is built for Vios Prycegas Tambulig Station as a rural LPG dealer management system.
