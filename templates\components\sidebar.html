<!-- Sidebar Navigation Component -->
<div class="fixed inset-y-0 left-0 z-50 w-64 sidebar transform transition-transform duration-300 ease-in-out md:translate-x-0"
     :class="{ 'sidebar-collapsed': sidebarCollapsed, '-translate-x-full': !sidebarOpen }"
     x-show="sidebarOpen || window.innerWidth >= 768"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="-translate-x-full"
     x-transition:enter-end="translate-x-0"
     x-transition:leave="transition ease-in duration-300"
     x-transition:leave-start="translate-x-0"
     x-transition:leave-end="-translate-x-full">
    
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between h-16 px-4 border-b" style="border-color: var(--border-dark);">
        <a href="{% url 'core:dashboard' %}" class="flex items-center">
            <h1 class="text-xl font-bold" style="color: var(--primary-orange);" x-show="!sidebarCollapsed">Prycegas</h1>
            <h1 class="text-xl font-bold" style="color: var(--primary-orange);" x-show="sidebarCollapsed" x-cloak>P</h1>
        </a>
        <button @click="sidebarCollapsed = !sidebarCollapsed" 
                class="sidebar-toggle md:block hidden">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M11 19l-7-7 7-7m8 14l-7-7 7-7" x-show="!sidebarCollapsed"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M13 5l7 7-7 7M5 5l7 7-7 7" x-show="sidebarCollapsed" x-cloak></path>
            </svg>
        </button>
    </div>
    
    <!-- Sidebar Navigation -->
    <nav class="mt-8 px-4">
        {% if user.is_authenticated %}
            {% if user.is_staff %}
                <!-- Admin Navigation -->
                <div class="space-y-2">
                    <a href="{% url 'core:admin_dashboard' %}" class="sidebar-nav-item">
                        <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Dashboard</span>
                    </a>
                    <a href="{% url 'core:orders' %}" class="sidebar-nav-item">
                        <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Orders</span>
                    </a>
                    <a href="{% url 'core:inventory' %}" class="sidebar-nav-item">
                        <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Inventory</span>
                    </a>
                    <a href="{% url 'core:reports' %}" class="sidebar-nav-item">
                        <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Reports</span>
                    </a>
                </div>
            {% else %}
                <!-- Customer Navigation -->
                <div class="space-y-2">
                    <a href="{% url 'core:customer_dashboard' %}" class="sidebar-nav-item">
                        <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                        </svg>
                        <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Dashboard</span>
                    </a>
                    <a href="{% url 'core:place_order' %}" class="sidebar-nav-item">
                        <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span class="sidebar-nav-text" x-show="!sidebarCollapsed">Place Order</span>
                    </a>
                    <a href="{% url 'core:my_orders' %}" class="sidebar-nav-item">
                        <svg class="sidebar-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="sidebar-nav-text" x-show="!sidebarCollapsed">My Orders</span>
                    </a>
                </div>
            {% endif %}
        {% endif %}
    </nav>
</div>

<!-- Mobile Sidebar Overlay -->
<div x-show="sidebarOpen && window.innerWidth < 768" 
     @click="sidebarOpen = false"
     x-transition:enter="transition-opacity ease-linear duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition-opacity ease-linear duration-300"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="sidebar-overlay md:hidden"
     x-cloak></div>
