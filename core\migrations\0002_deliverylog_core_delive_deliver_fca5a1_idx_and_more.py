# Generated by Django 5.2.4 on 2025-08-01 19:37

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddIndex(
            model_name='deliverylog',
            index=models.Index(fields=['-delivery_date'], name='core_delive_deliver_fca5a1_idx'),
        ),
        migrations.AddIndex(
            model_name='deliverylog',
            index=models.Index(fields=['product'], name='core_delive_product_e0bf42_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['-created_at'], name='core_order_created_929486_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['customer', 'status'], name='core_order_custome_7ac3b4_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['status'], name='core_order_status_6fe5d5_idx'),
        ),
    ]
