{% extends 'base/dashboard.html' %}

{% block title %}My Orders - Prycegas{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">My Orders</h1>
            <p class="mt-2 text-sm text-gray-700">View and track all your LPG orders.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <a href="{% url 'core:place_order' %}" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Place New Order
            </a>
        </div>
    </div>
    
    <!-- Filter section -->
    <div class="mt-6 bg-white shadow rounded-lg p-4">
        <div class="flex flex-wrap items-center gap-4">
            <div class="flex items-center space-x-2">
                <label for="status-filter" class="text-sm font-medium text-gray-700">Filter by status:</label>
                <select id="status-filter" 
                        hx-get="{% url 'core:my_orders' %}" 
                        hx-target="#orders-container" 
                        hx-include="[name='status']"
                        name="status"
                        class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="">All Orders</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
    </div>
    
    <!-- Orders list -->
    <div id="orders-container" class="mt-6">
        {% include 'partials/orders_list.html' %}
    </div>
</div>
{% endblock %}
