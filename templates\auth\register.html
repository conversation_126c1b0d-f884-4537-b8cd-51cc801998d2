{% extends 'base/landing.html' %}

{% block title %}Register - Prycegas{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-8 sm:py-12 px-2 sm:px-4 lg:px-8">
    <div class="max-w-sm sm:max-w-md lg:max-w-lg w-full space-y-6 sm:space-y-8">
        <div>
            <h2 class="mt-4 sm:mt-6 text-center text-2xl sm:text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-xs sm:text-sm text-gray-600">
                Or
                <a href="{% url 'auth:login' %}" class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
                    sign in to existing account
                </a>
            </p>
        </div>
        
        <form class="mt-8 space-y-6" method="post" hx-post="{% url 'auth:register' %}" hx-target="#register-form" hx-swap="outerHTML">
            <div id="register-form">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        {{ form.non_field_errors }}
                    </div>
                {% endif %}
                
                <div class="space-y-3 sm:space-y-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div>
                            <label for="{{ form.first_name.id_for_label }}" class="block text-xs sm:text-sm font-medium text-gray-700 mb-1">First Name</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="text-red-600 text-xs sm:text-sm mt-1">{{ form.first_name.errors }}</div>
                            {% endif %}
                        </div>
                        <div>
                            <label for="{{ form.last_name.id_for_label }}" class="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Last Name</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="text-red-600 text-xs sm:text-sm mt-1">{{ form.last_name.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">Username</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.username.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">Email</label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.email.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        {{ form.phone_number }}
                        {% if form.phone_number.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.phone_number.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.address.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">Password</label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.password1.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                            <div class="text-red-600 text-sm mt-1">{{ form.password2.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Create Account
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}
