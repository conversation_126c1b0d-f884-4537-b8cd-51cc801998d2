from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView, LogoutView
from django.contrib import messages
from django.urls import reverse_lazy
from core.forms import CustomerRegistrationForm
from core.models import Customer


class CustomLoginView(LoginView):
    """Custom login view with HTMX support"""
    template_name = 'auth/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        if self.request.user.is_staff:
            return reverse_lazy('core:admin_dashboard')
        return reverse_lazy('core:customer_dashboard')

    def form_valid(self, form):
        messages.success(self.request, f'Welcome back, {form.get_user().get_full_name() or form.get_user().username}!')
        return super().form_valid(form)


class CustomLogoutView(LogoutView):
    """Custom logout view that accepts both GET and POST"""
    next_page = reverse_lazy('core:home')
    http_method_names = ['get', 'post']  # Allow both GET and POST

    def get(self, request, *args, **kwargs):
        """Handle GET request - logout and redirect"""
        if request.user.is_authenticated:
            messages.info(request, 'You have been logged out successfully.')
        return self.post(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """Handle POST request - standard logout"""
        if request.user.is_authenticated:
            messages.info(request, 'You have been logged out successfully.')
        return super().post(request, *args, **kwargs)


def register(request):
    """Customer registration view"""
    if request.user.is_authenticated:
        return redirect('core:dashboard')

    if request.method == 'POST':
        form = CustomerRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            username = form.cleaned_data.get('username')
            messages.success(request, f'Account created for {username}! You can now log in.')

            # Auto-login the user
            user = authenticate(username=username, password=form.cleaned_data.get('password1'))
            if user:
                login(request, user)
                return redirect('core:customer_dashboard')
            return redirect('auth:login')
    else:
        form = CustomerRegistrationForm()

    return render(request, 'auth/register.html', {'form': form})


@login_required
def profile(request):
    """User profile view"""
    try:
        customer = request.user.customer
    except Customer.DoesNotExist:
        customer = None

    context = {
        'customer': customer
    }
    return render(request, 'auth/profile.html', context)
