from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from decimal import Decimal
from .models import Customer, LPGProduct, Inventory, Order, DeliveryLog


class ModelTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create customer profile
        self.customer = Customer.objects.create(
            user=self.user,
            phone_number='09123456789',
            address='Test Address, Tambulig'
        )

        # Create LPG product
        self.product = LPGProduct.objects.create(
            name='11kg LPG Tank',
            weight_kg=Decimal('11.0'),
            price=Decimal('850.00'),
            description='Test product'
        )

        # Create inventory
        self.inventory = Inventory.objects.create(
            product=self.product,
            current_stock=50,
            minimum_stock=10
        )

    def test_customer_creation(self):
        """Test customer model creation"""
        self.assertEqual(self.customer.user.username, 'testuser')
        self.assertEqual(self.customer.phone_number, '09123456789')
        self.assertIn('Test Address', self.customer.address)

    def test_product_creation(self):
        """Test LPG product creation"""
        self.assertEqual(self.product.name, '11kg LPG Tank')
        self.assertEqual(self.product.price, Decimal('850.00'))
        self.assertTrue(self.product.is_active)

    def test_inventory_low_stock(self):
        """Test inventory low stock detection"""
        self.assertFalse(self.inventory.is_low_stock)

        # Set stock to minimum level
        self.inventory.current_stock = 10
        self.inventory.save()
        self.assertTrue(self.inventory.is_low_stock)

    def test_order_creation(self):
        """Test order creation and total calculation"""
        order = Order.objects.create(
            customer=self.customer,
            product=self.product,
            quantity=2,
            delivery_type='delivery',
            delivery_address=self.customer.address
        )

        # Test auto-calculation of total amount
        expected_total = self.product.price * 2
        self.assertEqual(order.total_amount, expected_total)
        self.assertEqual(order.status, 'pending')

    def test_delivery_log_inventory_update(self):
        """Test that delivery log updates inventory"""
        initial_stock = self.inventory.current_stock

        # Create admin user for delivery log
        admin_user = User.objects.create_user(
            username='admin',
            password='adminpass',
            is_staff=True
        )

        # Create delivery log
        delivery = DeliveryLog.objects.create(
            product=self.product,
            quantity_received=20,
            supplier_name='Test Supplier',
            cost_per_unit=Decimal('700.00'),
            created_by=admin_user
        )

        # Check inventory was updated
        self.inventory.refresh_from_db()
        self.assertEqual(self.inventory.current_stock, initial_stock + 20)

        # Check total cost calculation
        expected_total = Decimal('700.00') * 20
        self.assertEqual(delivery.total_cost, expected_total)


class ViewTestCase(TestCase):
    def setUp(self):
        """Set up test data for view tests"""
        self.client = Client()

        # Create regular user
        self.user = User.objects.create_user(
            username='customer',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create admin user
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )

        # Create customer profile
        self.customer = Customer.objects.create(
            user=self.user,
            phone_number='09123456789',
            address='Test Address, Tambulig'
        )

        # Create test product
        self.product = LPGProduct.objects.create(
            name='11kg LPG Tank',
            weight_kg=Decimal('11.0'),
            price=Decimal('850.00')
        )

        # Create inventory
        self.inventory = Inventory.objects.create(
            product=self.product,
            current_stock=50,
            minimum_stock=10
        )

    def test_home_page(self):
        """Test home page loads correctly"""
        response = self.client.get(reverse('core:home'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Vios Prycegas Tambulig Station')

    def test_customer_dashboard_requires_login(self):
        """Test customer dashboard requires authentication"""
        response = self.client.get(reverse('core:customer_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_customer_dashboard_access(self):
        """Test customer can access dashboard"""
        self.client.login(username='customer', password='testpass123')
        response = self.client.get(reverse('core:customer_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Welcome')

    def test_admin_dashboard_requires_staff(self):
        """Test admin dashboard requires staff privileges"""
        self.client.login(username='customer', password='testpass123')
        response = self.client.get(reverse('core:admin_dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to customer dashboard

    def test_admin_dashboard_access(self):
        """Test admin can access admin dashboard"""
        self.client.login(username='admin', password='adminpass123')
        response = self.client.get(reverse('core:admin_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Dealer Dashboard')

    def test_place_order_view(self):
        """Test place order functionality"""
        self.client.login(username='customer', password='testpass123')

        # Test GET request
        response = self.client.get(reverse('core:place_order'))
        self.assertEqual(response.status_code, 200)

        # Test POST request
        order_data = {
            'product': self.product.id,
            'quantity': 2,
            'delivery_type': 'delivery',
            'delivery_address': 'Test Delivery Address',
            'notes': 'Test order'
        }

        response = self.client.post(reverse('core:place_order'), order_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful order

        # Check order was created
        order = Order.objects.filter(customer=self.customer).first()
        self.assertIsNotNone(order)
        self.assertEqual(order.quantity, 2)
        self.assertEqual(order.product, self.product)

    def test_my_orders_view(self):
        """Test customer orders view"""
        self.client.login(username='customer', password='testpass123')

        # Create test order
        Order.objects.create(
            customer=self.customer,
            product=self.product,
            quantity=1,
            delivery_type='pickup',
            total_amount=self.product.price
        )

        response = self.client.get(reverse('core:my_orders'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'My Orders')

    def test_inventory_management_view(self):
        """Test inventory management view"""
        self.client.login(username='admin', password='adminpass123')
        response = self.client.get(reverse('core:inventory'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Inventory Management')

    def test_reports_view(self):
        """Test reports view"""
        self.client.login(username='admin', password='adminpass123')
        response = self.client.get(reverse('core:reports'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Business Reports')
